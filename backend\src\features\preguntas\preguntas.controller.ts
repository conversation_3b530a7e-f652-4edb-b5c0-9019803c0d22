import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/auth/jwt-auth.guard';
import { PreguntasService } from './preguntas.service';
import { CreateRespuestaDto } from './dto/create-respuesta.dto';

@ApiTags('Preguntas')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('respuestas-pregunta')
export class PreguntasController {
  constructor(private readonly preguntasService: PreguntasService) {}

  @Post()
  @ApiOperation({ summary: 'Submit an answer to a question' })
  @ApiResponse({
    status: 200,
    description: 'The answer has been successfully submitted.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  create(@Body() createRespuestaDto: CreateRespuestaDto) {
    return this.preguntasService.addRespuesta(createRespuestaDto);
  }
}
