import { useState, useMemo, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useEmpresas } from '../hooks/useEmpresas';
import { LoadingSpinner } from '../../../components/ui/LoadingSpinner';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';

interface EmpresaListItem {
  id: string;
  nombre: string;
  logo_url?: string;
  sector?: string;
  total_usuarios: number;
  total_procesos: number;
}

/**
 * Company selector component for admin panel
 * Provides dropdown with search functionality for client companies
 */
export const EmpresaSelector = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedEmpresa, setSelectedEmpresa] = useState<EmpresaListItem | null>(null);
  const navigate = useNavigate();
  const { data, isLoading, error } = useEmpresas();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter companies based on search term
  const filteredEmpresas = useMemo(() => {
    if (!data?.empresas) return [];

    if (!searchTerm.trim()) return data.empresas;

    const term = searchTerm.toLowerCase();
    return data.empresas.filter(empresa =>
      empresa.nombre.toLowerCase().includes(term) ||
      empresa.sector?.toLowerCase().includes(term)
    );
  }, [data?.empresas, searchTerm]);

  const handleEmpresaSelect = (empresa: EmpresaListItem) => {
    setSelectedEmpresa(empresa);
    setSearchTerm('');
    setIsDropdownOpen(false);
  };

  const handleNavigateToPanel = () => {
    if (selectedEmpresa) {
      navigate(`/admin/gestor/${selectedEmpresa.id}`);
    }
  };

  const handleInputFocus = () => {
    setIsDropdownOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setIsDropdownOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-600 text-lg font-medium mb-2">
            Error al cargar empresas
          </div>
          <div className="text-gray-600">
            Por favor, intenta de nuevo más tarde
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Gestor de Clientes
        </h1>
        <p className="text-gray-600">
          Selecciona una empresa para acceder a su panel de gestión
        </p>
      </div>

      {/* Company Selector */}
      <div className="space-y-6">
        {/* Selected Company Display */}
        {selectedEmpresa && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {selectedEmpresa.logo_url ? (
                  <img
                    src={selectedEmpresa.logo_url}
                    alt={`${selectedEmpresa.nombre} logo`}
                    className="w-10 h-10 rounded-lg object-cover mr-3"
                  />
                ) : (
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-bold">
                      {selectedEmpresa.nombre.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div>
                  <h3 className="font-semibold text-blue-900">
                    {selectedEmpresa.nombre}
                  </h3>
                  {selectedEmpresa.sector && (
                    <p className="text-blue-600 text-sm">{selectedEmpresa.sector}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm text-blue-600">
                    {selectedEmpresa.total_usuarios} usuarios • {selectedEmpresa.total_procesos} procesos
                  </div>
                </div>
                <Button
                  onClick={handleNavigateToPanel}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Acceder al Panel
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Dropdown Selector */}
        <div className="relative" ref={dropdownRef}>
          <div className="relative">
            <Input
              ref={inputRef}
              type="text"
              placeholder={selectedEmpresa ? "Buscar otra empresa..." : "Buscar empresa por nombre o sector..."}
              value={searchTerm}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              className="w-full text-lg py-3 pr-10"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg
                className={`w-5 h-5 text-gray-400 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* Dropdown Options */}
          {isDropdownOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : filteredEmpresas.length > 0 ? (
                <div className="py-2">
                  {filteredEmpresas.map((empresa) => (
                    <div
                      key={empresa.id}
                      onClick={() => handleEmpresaSelect(empresa)}
                      className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {empresa.logo_url ? (
                            <img
                              src={empresa.logo_url}
                              alt={`${empresa.nombre} logo`}
                              className="w-8 h-8 rounded object-cover mr-3"
                            />
                          ) : (
                            <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
                              <span className="text-blue-600 font-bold text-sm">
                                {empresa.nombre.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">
                              {empresa.nombre}
                            </div>
                            {empresa.sector && (
                              <div className="text-sm text-gray-500">
                                {empresa.sector}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-600">
                            {empresa.total_usuarios} usuarios
                          </div>
                          <div className="text-sm text-gray-600">
                            {empresa.total_procesos} procesos
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="px-4 py-8 text-center text-gray-500">
                  {searchTerm ?
                    `No se encontraron empresas que coincidan con "${searchTerm}"` :
                    'No hay empresas disponibles'
                  }
                </div>
              )}
            </div>
          )}
        </div>

        {/* Instructions */}
        {!selectedEmpresa && (
          <div className="text-center py-8">
            <div className="text-gray-500">
              Haz clic en el campo de búsqueda para ver todas las empresas disponibles
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
