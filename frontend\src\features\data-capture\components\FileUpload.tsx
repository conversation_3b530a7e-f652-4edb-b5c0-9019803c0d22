import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import type { DropzoneOptions } from 'react-dropzone';

interface FileUploadProps {
  onUpload: (files: File[]) => void;
  options?: DropzoneOptions;
  disabled?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUpload, options, disabled }) => {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    onUpload(acceptedFiles);
  }, [onUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    ...options,
    disabled,
  });

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-300
      ${
        disabled
          ? 'bg-gray-100 cursor-not-allowed'
          : isDragActive
          ? 'border-indigo-500 bg-indigo-50'
          : 'border-gray-300 hover:border-gray-400 cursor-pointer'
      }`}
    >
      <input {...getInputProps()} />
      <p className="text-gray-500">
        {isDragActive
          ? 'Suelta el archivo aquí...'
          : 'Arrastra y suelta un archivo aquí, o haz clic para seleccionar uno.'}
      </p>
    </div>
  );
};

export default FileUpload;