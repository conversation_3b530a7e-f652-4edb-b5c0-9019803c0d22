import { ApiProperty } from '@nestjs/swagger';

export class FindingDetailDto {
  @ApiProperty({ description: 'Finding ID' })
  id: string;

  @ApiProperty({ description: 'Finding title' })
  titulo: string;

  @ApiProperty({ description: 'Finding description' })
  descripcion: string;

  @ApiProperty({ description: 'Detailed description' })
  descripcion_detallada: string;

  @ApiProperty({ description: 'Finding type' })
  tipo: string;

  @ApiProperty({ description: 'Department name' })
  departamento: string;

  @ApiProperty({ description: 'Related process', required: false })
  proceso_relacionado?: {
    id: string;
    nombre_proceso: string;
  };

  @ApiProperty({ description: 'Impact level', required: false })
  nivel_impacto?: string;

  @ApiProperty({ description: 'Priority level', required: false })
  prioridad?: string;

  @ApiProperty({
    description: 'Estimated time savings in minutes',
    required: false,
  })
  tiempo_ahorro_estimado?: number;

  @ApiProperty({ description: 'Implementation effort', required: false })
  esfuerzo_implementacion?: string;

  @ApiProperty({ description: 'Potential ROI', required: false })
  roi_potencial?: string;

  @ApiProperty({ description: 'Current status', required: false })
  estado?: string;

  @ApiProperty({ description: 'Identified by user' })
  identificado_por: {
    id: string;
    nombre: string;
    apellidos: string;
    cargo: string;
    departamento: string;
  };

  @ApiProperty({ description: 'Creation date' })
  created_at: string;

  @ApiProperty({ description: 'Last update date' })
  updated_at: string;

  @ApiProperty({ description: 'Additional information', required: false })
  info_adicional?: string;

  @ApiProperty({ description: 'Recommended actions', required: false })
  acciones_recomendadas?: string;
}
