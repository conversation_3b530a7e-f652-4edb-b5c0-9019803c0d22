import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from 'src/common/database.service';
import { TableTypes } from 'src/common/database.types';
import { CreateFuenteInformacionDto } from './dto/create-fuente-informacion.dto';
import { UpdateFuenteInformacionDto } from './dto/update-fuente-informacion.dto';

type FuenteInformacion = TableTypes['procesos_fuentes_informacion']['select'];

@Injectable()
export class ProcesosFuentesInformacionService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(
    createDto: CreateFuenteInformacionDto,
  ): Promise<FuenteInformacion> {
    const response = await this.databaseService
      .getClient()
      .from('procesos_fuentes_informacion')
      .insert(createDto)
      .select()
      .single<FuenteInformacion>();

    const data = response.data;
    const error = response.error;

    if (error || !data) {
      throw new InternalServerErrorException(
        'Failed to create information source.',
        error?.message,
      );
    }
    return data;
  }

  async findAll(procesoClienteId: string): Promise<FuenteInformacion[]> {
    const { data, error } = await this.databaseService
      .getClient()
      .from('procesos_fuentes_informacion')
      .select('*')
      .eq('proceso_cliente_id', procesoClienteId);

    if (error) {
      throw new InternalServerErrorException(
        'Failed to retrieve information sources.',
        error.message,
      );
    }
    return data as FuenteInformacion[];
  }

  async findOne(id: string): Promise<FuenteInformacion> {
    const response = await this.databaseService
      .getClient()
      .from('procesos_fuentes_informacion')
      .select('*')
      .eq('id', id)
      .single<FuenteInformacion>();

    const data = response.data;
    const error = response.error;

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(
          `Information source with ID ${id} not found.`,
        );
      }
      throw new InternalServerErrorException(
        'Failed to retrieve information source.',
        error.message,
      );
    }

    if (!data) {
      throw new NotFoundException(
        `Information source with ID ${id} not found.`,
      );
    }

    return data;
  }

  async update(
    id: string,
    updateDto: UpdateFuenteInformacionDto,
  ): Promise<FuenteInformacion> {
    const response = await this.databaseService
      .getClient()
      .from('procesos_fuentes_informacion')
      .update(updateDto)
      .eq('id', id)
      .select()
      .single<FuenteInformacion>();

    const data = response.data;
    const error = response.error;

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(
          `Information source with ID ${id} not found.`,
        );
      }
      throw new InternalServerErrorException(
        'Failed to update information source.',
        error.message,
      );
    }

    if (!data) {
      throw new NotFoundException(
        `Information source with ID ${id} not found.`,
      );
    }

    return data;
  }

  async remove(id: string): Promise<void> {
    const { error } = await this.databaseService
      .getClient()
      .from('procesos_fuentes_informacion')
      .delete()
      .eq('id', id);

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(
          `Information source with ID ${id} not found.`,
        );
      }
      throw new InternalServerErrorException(
        'Failed to delete information source.',
        error.message,
      );
    }
  }
}
