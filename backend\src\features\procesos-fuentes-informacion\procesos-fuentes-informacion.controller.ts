import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/auth/jwt-auth.guard';
import { ProcesosFuentesInformacionService } from './procesos-fuentes-informacion.service';
import { CreateFuenteInformacionDto } from './dto/create-fuente-informacion.dto';
import { UpdateFuenteInformacionDto } from './dto/update-fuente-informacion.dto';

@ApiTags('Procesos - Fuentes de Información')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('procesos-fuentes-informacion')
export class ProcesosFuentesInformacionController {
  constructor(
    private readonly fuentesService: ProcesosFuentesInformacionService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new information source' })
  @ApiResponse({
    status: 201,
    description: 'The record has been successfully created.',
  })
  create(@Body() createDto: CreateFuenteInformacionDto) {
    return this.fuentesService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all information sources for a process' })
  findAll(@Query('procesoClienteId') procesoClienteId: string) {
    return this.fuentesService.findAll(procesoClienteId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an information source by ID' })
  findOne(@Param('id') id: string) {
    return this.fuentesService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an information source' })
  update(
    @Param('id') id: string,
    @Body() updateDto: UpdateFuenteInformacionDto,
  ) {
    return this.fuentesService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an information source' })
  @ApiResponse({
    status: 204,
    description: 'The record has been successfully deleted.',
  })
  remove(@Param('id') id: string) {
    return this.fuentesService.remove(id);
  }
}
