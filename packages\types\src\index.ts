export type User = {
  id: string;
  nombre: string;
  apellidos: string | null;
  email: string;
  rol: 'admin_aceleralia' | 'empleado_aceleralia' | 'cliente_gerente' | 'cliente_empleado';
  puntos: number;
};
export type Grabacion = {
  id: string;
  entidad_relacionada_id: string;
  entidad_relacionada_tipo: 'proceso_cliente' | 'pregunta_cliente' | 'tarea_cliente';
  usuario_id: string;
  url_almacenamiento: string;
  estado_procesamiento: 'pendiente' | 'en_procesamiento' | 'completado' | 'error';
  tipo_grabacion: 'video_pantalla' | 'solo_audio';
  transcripcion?: string | null;
  duracion_segundos?: number | null;
  info_adicional?: string | null;
  created_at: string;
  updated_at: string;
};

export type RetosSubtareas = {
  id: string;
  titulo: string;
  descripcion: string | null;
  tipo_accion: 'definir_proceso' | 'definir_tarea' | 'definir_duracion_proceso' | 'responder_pregunta';
  estado: 'pendiente' | 'completado' | 'expirado';
};

export type RetosUsuarios = {
  id: string;
  titulo: string;
  descripcion: string | null;
  puntos_recompensa: number;
  estado: 'pendiente' | 'completado' | 'expirado';
  prioridad: number | null;
  url_destino: string | null;
  retos_subtareas: RetosSubtareas[];
};

export type HomeDashboard = {
  dailyChallenges: RetosUsuarios[];
  sprint: {
    title: string;
    progressPercentage: number;
  };
  kpis: {
    points: number;
    streak: number | null;
    completedChallenges: number;
  };
};

// Manager Dashboard Types
export type ChartDataPoint = {
  label: string;
  value: number;
  color?: string;
};

export type KpiData = {
  title: string;
  value: number;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
};

export type TopPerformer = {
  id: string;
  nombre: string;
  puntos: number;
  departamento?: string;
};

export type ManagerChallenge = {
  id: string;
  titulo: string;
  descripcion: string;
  puntos_recompensa: number;
  estado: string;
};

export type ManagerDashboardSummary = {
  kpis: KpiData[];
  processesByDepartment: ChartDataPoint[];
  findingsByType: ChartDataPoint[];
  recentActivity: ChartDataPoint[];
  topPerformers: TopPerformer[];
  managerChallenge?: ManagerChallenge;
};

export type ProcessListItem = {
  id: string;
  nombre_proceso: string;
  descripcion_breve: string;
  departamento: string;
  responsable: string;
  duracion_minutos?: number;
  frecuencia: string;
  created_at: string;
  total_tareas: number;
  total_hallazgos: number;
};

export type ProcessListResponse = {
  data: ProcessListItem[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

export type FindingListItem = {
  id: string;
  titulo: string;
  descripcion: string;
  tipo: string;
  departamento: string;
  proceso_relacionado?: string;
  nivel_impacto?: string;
  prioridad?: string;
  created_at: string;
  identificado_por: string;
};

export type FindingListResponse = {
  data: FindingListItem[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

export type ProcessDetail = {
  id: string;
  nombre_proceso: string;
  descripcion_breve: string;
  descripcion_detallada: string;
  departamento: string;
  duracion_minutos?: number;
  frecuencia: string;
  complejidad?: string;
  potencial_automatizacion?: string;
  created_at: string;
  updated_at: string;
  tareas: {
    id: string;
    nombre_tarea: string;
    descripcion: string;
    duracion_minutos?: number;
    orden: number;
    responsable?: string;
  }[];
  responsables: {
    id: string;
    nombre: string;
    apellidos: string;
    cargo: string;
    departamento: string;
    email: string;
  }[];
  fuentes_informacion: {
    id: string;
    nombre_informacion: string;
    descripcion?: string;
    formato: string;
    persona_responsable: string;
    url_adjunto?: string;
  }[];
  total_hallazgos: number;
};

export type FindingDetail = {
  id: string;
  titulo: string;
  descripcion: string;
  descripcion_detallada: string;
  tipo: string;
  departamento: string;
  proceso_relacionado?: {
    id: string;
    nombre_proceso: string;
  };
  nivel_impacto?: string;
  prioridad?: string;
  tiempo_ahorro_estimado?: number;
  esfuerzo_implementacion?: string;
  roi_potencial?: string;
  estado?: string;
  identificado_por: {
    id: string;
    nombre: string;
    apellidos: string;
    cargo: string;
    departamento: string;
  };
  created_at: string;
  updated_at: string;
  info_adicional?: string;
  acciones_recomendadas?: string;
};