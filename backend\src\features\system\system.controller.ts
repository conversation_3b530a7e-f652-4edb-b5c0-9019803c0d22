import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { SystemService } from './system.service';
import { AwardPointsDto } from './dto/award-points.dto';
import { ApiKeyGuard } from 'src/common/auth/guards/api-key.guard';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('System')
@Controller('system')
export class SystemController {
  constructor(private readonly systemService: SystemService) {}

  @UseGuards(ApiKeyGuard)
  @Post('award-points')
  awardPoints(@Body() awardPointsDto: AwardPointsDto) {
    return this.systemService.awardPoints(awardPointsDto);
  }
}
