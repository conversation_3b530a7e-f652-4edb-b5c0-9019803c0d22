import React, { useEffect, useState } from 'react';
import { useAuthStore } from '../store/useAuthStore';
import { Outlet } from 'react-router-dom';
import LoadingSpinner from '../../../components/ui/LoadingSpinner';

const PersistLogin: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const { token } = useAuthStore();

  useEffect(() => {
    // The zustand persist middleware rehydrates the store asynchronously.
    // We can listen to the storage event or simply give it a moment to rehydrate.
    // A simple timeout is often sufficient for this purpose.
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500); // Adjust time as needed, or use a more robust rehydration check

    return () => clearTimeout(timer);
  }, []);

  // This effect will run once on mount and then only when the token changes.
  // It handles the initial loading state.
  useEffect(() => {
    if (token !== undefined) {
      setIsLoading(false);
    }
  }, [token]);

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return <Outlet />;
};

export default PersistLogin;