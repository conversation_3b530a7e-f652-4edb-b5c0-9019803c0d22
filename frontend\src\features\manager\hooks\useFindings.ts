import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { type FindingListResponse, type FindingDetail } from '@aceleralia/types';

interface FindingListParams {
  page?: number;
  limit?: number;
  tipo?: string;
  departamento?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

const getFindings = async (params: FindingListParams): Promise<FindingListResponse> => {
  const response = await apiClient.get('/manager/hallazgos-clientes', { params });
  return response.data;
};

const getFindingDetail = async (findingId: string): Promise<FindingDetail> => {
  const response = await apiClient.get(`/manager/hallazgos-clientes/${findingId}`);
  return response.data;
};

export const useFindings = (params: FindingListParams) => {
  return useQuery({
    queryKey: ['managerFindings', params],
    queryFn: () => getFindings(params),
  });
};

export const useFindingDetail = (findingId: string) => {
  return useQuery({
    queryKey: ['findingDetail', findingId],
    queryFn: () => getFindingDetail(findingId),
    enabled: !!findingId,
  });
};
