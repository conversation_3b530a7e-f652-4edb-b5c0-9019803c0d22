import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './common/database.module';
import { GrabacionesModule } from './features/grabaciones/grabaciones.module';
import { AuthModule } from './features/auth/auth.module';
import { SharedModule } from './shared/shared.module';
import { ProcesosClientesModule } from './features/procesos-clientes/procesos-clientes.module';
import { PreguntasModule } from './features/preguntas/preguntas.module';
import { ProcesosFuentesInformacionModule } from './features/procesos-fuentes-informacion/procesos-fuentes-informacion.module';
import { SystemModule } from './features/system/system.module';
import { RetosModule } from './features/retos/retos.module';
import { RankingModule } from './features/ranking/ranking.module';
import { DashboardModule } from './features/dashboard/dashboard.module';
import { ManagerModule } from './features/manager/manager.module';
import { AdminModule } from './features/admin/admin.module';
import n8nConfig from './config/n8n.config';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, load: [n8nConfig] }),
    DatabaseModule,
    AuthModule,
    SharedModule,
    GrabacionesModule,
    ProcesosClientesModule,
    PreguntasModule,
    ProcesosFuentesInformacionModule,
    SystemModule,
    RetosModule,
    RankingModule,
    DashboardModule,
    ManagerModule,
    AdminModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
