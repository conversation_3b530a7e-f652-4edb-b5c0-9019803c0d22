import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../../../lib/api';
import { useToastStore } from '../../../store/toast';

interface CreateUsuarioRequest {
  email: string;
  password: string;
  rol: 'cliente_gerente' | 'cliente_empleado';
  empresa_id: string;
  nombre: string;
  apellidos: string;
}

interface CreateUsuarioResponse {
  id: string;
  email: string;
  nombre_completo: string;
  rol: string;
  message: string;
}

interface VincularPersonaRequest {
  usuario_id: string;
}

interface VincularPersonaResponse {
  message: string;
  persona_id: string;
  usuario_id: string;
}

/**
 * Hook to create a new user account
 */
export const useCreateUsuario = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: async (data: CreateUsuarioRequest): Promise<CreateUsuarioResponse> => {
      const response = await api.post<CreateUsuarioResponse>('/admin/usuarios', data);
      return response.data;
    },
    onSuccess: (data) => {
      showToast(data.message, 'success');
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['admin', 'gestor-panel'] });
    },
    onError: (error: any) => {
      showToast(error.response?.data?.message || 'Error al crear usuario', 'error');
    },
  });
};

/**
 * Hook to link a user to a person
 */
export const useVincularPersona = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: async ({ personaId, data }: { personaId: string; data: VincularPersonaRequest }): Promise<VincularPersonaResponse> => {
      const response = await api.patch<VincularPersonaResponse>(`/admin/personas/${personaId}/vincular`, data);
      return response.data;
    },
    onSuccess: (data) => {
      showToast(data.message, 'success');
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['admin', 'gestor-panel'] });
    },
    onError: (error: any) => {
      showToast(error.response?.data?.message || 'Error al vincular usuario', 'error');
    },
  });
};
