# Sprint Log: Real-time Features - 2025-07-14

## 1. Sprint Goal(s)

*   Goal 1: Enhance the user experience by providing immediate feedback and updates for gamification events.
*   Goal 2: Integrate Supabase Realtime services to create a more dynamic and engaging application.

## 1.b Relevant Feature Documents

*   `docs/modulos/4_Core_Gamification_and_Challenges.md`

## 2. Planned Tasks

*   [ ] **Task 1: Implement Real-time Ranking Updates**
    *   On the Ranking Page (`/portal/ranking`), subscribe to changes on the `usuarios` table using Supabase Realtime.
    *   When an update event is received (e.g., another user's `puntos` change), invalidate the TanStack Query cache for the `GET /ranking` endpoint to trigger a refetch.
    *   Ensure the UI updates smoothly without a full page reload.
*   [ ] **Task 2: Implement Real-time Notifications**
    *   Create a global `<GlobalToast>` component that is always present in the application layout.
    *   Create a `useNotifications` hook that subscribes to inserts on the `notificaciones` table for the current user.
    *   When a new notification is received, the hook should trigger the `<GlobalToast>` component to display the `titulo` and `mensaje`.
*   [ ] **Task 3: Backend Support for Notifications**
    *   Ensure that the asynchronous n8n workflows (and other backend services) correctly create records in the `notificaciones` table when a challenge is completed and points are awarded.
    *   The notification payload should include a user-friendly message and a `tipo_notificacion`.

## 3. Current Progress & Work Log

*   **Current Status:** This sprint has been planned and is ready for implementation.

## 4. Pending Tasks (Within this Sprint)

*   All tasks are pending.

## 5. Key Decisions Made

*   **Decision:** The real-time ranking will use a "refetch" strategy rather than attempting to manually update the client-side state. This is simpler to implement and less prone to data consistency issues.

## 6. Blockers / Issues Encountered

*   No blockers have been identified.

## 7. Sprint Outcome & Summary

*   This section will be completed at the end of the sprint.

## 8. Follow-up Actions / Next Steps

*   Proceed with the implementation of the planned tasks in the `code` mode.