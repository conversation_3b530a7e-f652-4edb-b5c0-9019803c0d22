import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindingListQueryDto {
  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: 'Items per page', required: false, default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ description: 'Filter by finding type', required: false })
  @IsOptional()
  @IsString()
  tipo?: string;

  @ApiProperty({ description: 'Filter by department', required: false })
  @IsOptional()
  @IsString()
  departamento?: string;

  @ApiProperty({
    description: 'Search in title or description',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Sort field',
    required: false,
    default: 'created_at',
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'created_at';

  @ApiProperty({
    description: 'Sort order',
    required: false,
    default: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class FindingListItemDto {
  @ApiProperty({ description: 'Finding ID' })
  id: string;

  @ApiProperty({ description: 'Finding title' })
  titulo: string;

  @ApiProperty({ description: 'Finding description' })
  descripcion: string;

  @ApiProperty({ description: 'Finding type' })
  tipo: string;

  @ApiProperty({ description: 'Department name' })
  departamento: string;

  @ApiProperty({ description: 'Related process name', required: false })
  proceso_relacionado?: string;

  @ApiProperty({ description: 'Impact level', required: false })
  nivel_impacto?: string;

  @ApiProperty({ description: 'Priority level', required: false })
  prioridad?: string;

  @ApiProperty({ description: 'Creation date' })
  created_at: string;

  @ApiProperty({ description: 'Identified by user' })
  identificado_por: string;
}

export class FindingListResponseDto {
  @ApiProperty({ description: 'List of findings', type: [FindingListItemDto] })
  data: FindingListItemDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
