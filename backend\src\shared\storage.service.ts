import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { DatabaseService } from '../common/database.service';

// Interface for uploaded file to resolve typing issues
interface UploadedFile {
  originalname: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private readonly supabase: SupabaseClient;

  constructor(private readonly databaseService: DatabaseService) {
    this.supabase = this.databaseService.getClient();
  }

  async uploadToTemporal(
    file: UploadedFile,
    bucket: string,
    path: string,
  ): Promise<string> {
    const { data, error } = await this.supabase.storage
      .from(bucket)
      .upload(path, file.buffer, {
        contentType: file.mimetype,
      });

    if (error) {
      this.logger.error(
        `Error uploading file to Supabase Storage bucket "${bucket}" at path "${path}":`,
        error.message,
      );
      throw new InternalServerErrorException('Failed to upload file.');
    }

    this.logger.log(
      `Successfully uploaded file to Supabase Storage bucket "${bucket}" at path "${path}"`,
    );
    return data.path;
  }
}
