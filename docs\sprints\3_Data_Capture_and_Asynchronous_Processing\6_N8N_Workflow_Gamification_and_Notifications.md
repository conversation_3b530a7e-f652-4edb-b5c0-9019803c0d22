# Sprint 19: N8N Workflow - Gamification & Notifications

## 1. Sprint Goal
The goal of this sprint is to complete the asynchronous user feedback loop. After the core business logic is successfully executed, the n8n workflow will be extended to award points to the user and send them a real-time notification, confirming that their submission has been processed and valued.

## 2. Key Tasks
- **Integrate Gamification Logic:**
    - Add a new node or step in the n8n workflow that calls our backend's `GamificationService`.
    - This could be done via a dedicated webhook endpoint on our backend (e.g., `POST /system/award-points`) that can be called securely from n8n.
    - The call will pass the `userId` and the type of action completed to award the correct number of points and update the user's streak.
- **Create Notification Record:**
    - After awarding points, add a database node to create a new record in the `notificaciones` table.
    - The notification record should contain a clear title (e.g., "¡Reto Completado!"), a message ("Has ganado X puntos por definir el proceso Y"), and the `userId`.
- **Finalize `grabaciones` Record:**
    - Add a final database update step to change the `grabaciones` record's `estado_procesamiento` from `en_procesamiento` to `completado`.
- **Verify Frontend Real-time Update:**
    - Confirm that when the `notificaciones` record is created, the existing Supabase Realtime subscription on the frontend picks up the change and displays the toast notification to the user.

## 3. Acceptance Criteria
- After a submission is successfully processed by the AI, the correct number of points are added to the user's score in the `usuarios` table.
- A new record is created in the `notificaciones` table for the correct user.
- The `grabaciones` record is marked as `completado`.
- The user sees a real-time toast notification in the UI, informing them of their reward.
- The entire n8n workflow, from file orchestration to notification, runs successfully without manual intervention.

## 4. Work Completed
- **Backend `System` Module:**
    - Created a new `SystemModule` to handle internal, system-to-system communications.
    - Implemented a `SystemController` with a secure `/system/award-points` endpoint protected by an `ApiKeyGuard`.
    - The `SystemService` now calls the `GamificationService` to award points and mark subtasks as complete.
- **n8n Workflow Documentation:**
    - Created `docs/n8n/workflow_gamification_and_notifications.md` with detailed instructions for creating the n8n workflow, including the webhook trigger, expected payload, and all necessary steps.
- **Architecture Documentation:**
    - Updated `docs/ARCHITECTURE.md` to include the new `system` module in the file structure diagram.

## 5. Pending Tasks
- **n8n Workflow Implementation:** The n8n workflow needs to be created and tested by the user.
- **End-to-end Testing:** Once the n8n workflow is operational, the full flow from user submission to notification needs to be tested.