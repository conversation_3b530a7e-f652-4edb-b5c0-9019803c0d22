/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-argument */
import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { DatabaseService } from '../../common/database.service';
import {
  EmpresaListResponseDto,
  EmpresaListItemDto,
} from './dto/empresa-list.dto';
import {
  GestorPanelResponseDto,
  SprintDataDto,
  UsuarioPersonaDto,
} from './dto/gestor-panel.dto';
import {
  CreateUsuarioDto,
  CreateUsuarioResponseDto,
} from './dto/create-usuario.dto';
import {
  VincularPersonaDto,
  VincularPersonaResponseDto,
} from './dto/vincular-persona.dto';
import {
  CreateSprintDto,
  CreateSprintResponseDto,
} from './dto/create-sprint.dto';
import {
  CreateRetoManualDto,
  CreateRetoManualResponseDto,
} from './dto/create-reto-manual.dto';
import {
  PersonaDetalleResponseDto,
  PersonaKpiDto,
  ProcesoPersonaDto,
  HallazgoPersonaDto,
} from './dto/persona-detalle.dto';

/**
 * Service for admin panel functionality
 * Provides comprehensive management tools for Aceleralia administrators
 */
@Injectable()
export class AdminService {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * Get list of all companies for the company selector
   */
  async getEmpresas(): Promise<EmpresaListResponseDto> {
    // Use service client to bypass RLS for admin operations
    const { data: empresas, error } = await this.databaseService
      .getServiceClient()
      .from('empresas')
      .select(
        `
        id,
        nombre,
        logo_url,
        sector,
        usuarios:usuarios(count),
        procesos:procesos_clientes(count)
      `,
      )
      .eq('activo', true)
      .eq('tipo_relacion', 'Cliente');

    if (error) {
      throw new Error(`Error fetching companies: ${error.message}`);
    }

    const empresasFormatted: EmpresaListItemDto[] = empresas.map(
      (empresa: any) => ({
        id: empresa.id,
        nombre: empresa.nombre,
        logo_url: empresa.logo_url,
        sector: empresa.sector,
        total_usuarios: empresa.usuarios?.[0]?.count || 0,
        total_procesos: empresa.procesos?.[0]?.count || 0,
      }),
    );

    return { empresas: empresasFormatted };
  }

  /**
   * Get comprehensive panel data for a specific company
   */
  async getGestorPanel(empresaId: string): Promise<GestorPanelResponseDto> {
    // Verify company exists
    const { data: empresa, error: empresaError } = await this.databaseService
      .getClient()
      .from('empresas')
      .select('id, nombre, logo_url, sector')
      .eq('id', empresaId)
      .single();

    if (empresaError || !empresa) {
      throw new NotFoundException('Company not found');
    }

    // Get active sprint
    const sprintActivo = await this.getActiveSprint(empresaId);

    // Get sprint history
    const historialSprints = await this.getSprintHistory(empresaId);

    // Get users and persons data
    const usuariosPersonas = await this.getUsuariosPersonas(empresaId);

    // Get persons without user account
    const personasSinUsuario = await this.getPersonasSinUsuario(empresaId);

    return {
      empresa,
      sprint_activo: sprintActivo,
      historial_sprints: historialSprints,
      usuarios_personas: usuariosPersonas,
      personas_sin_usuario: personasSinUsuario,
    };
  }

  /**
   * Create a new user account
   */
  async createUsuario(
    createUsuarioDto: CreateUsuarioDto,
  ): Promise<CreateUsuarioResponseDto> {
    // First, create user in Supabase Auth
    const { data: authUser, error: authError } = await this.databaseService
      .getClient()
      .auth.admin.createUser({
        email: createUsuarioDto.email,
        password: createUsuarioDto.password,
        email_confirm: true,
      });

    if (authError || !authUser.user) {
      throw new BadRequestException(
        `Error creating auth user: ${authError?.message}`,
      );
    }

    // Then create user in our usuarios table
    const { data: usuario, error: usuarioError } = await this.databaseService
      .getClient()
      .from('usuarios')
      .insert({
        auth_user_id: authUser.user.id,
        email: createUsuarioDto.email,
        nombre: createUsuarioDto.nombre,
        apellidos: createUsuarioDto.apellidos,
        rol: createUsuarioDto.rol,
        empresa_id: createUsuarioDto.empresa_id,
        puntos: 0,
        numero_retos_diario: 1,
        racha_actual: 0,
      })
      .select()
      .single();

    if (usuarioError || !usuario) {
      // Rollback auth user creation if database insert fails
      await this.databaseService
        .getClient()
        .auth.admin.deleteUser(authUser.user.id);
      throw new BadRequestException(
        `Error creating user record: ${usuarioError?.message}`,
      );
    }

    return {
      id: usuario.id,
      email: usuario.email,
      nombre_completo: `${usuario.nombre} ${usuario.apellidos}`,
      rol: usuario.rol,
      message: 'Usuario creado exitosamente',
    };
  }

  /**
   * Link a user to a person
   */
  async vincularPersona(
    personaId: string,
    vincularDto: VincularPersonaDto,
  ): Promise<VincularPersonaResponseDto> {
    // Verify person exists and doesn't already have a user
    const { data: persona, error: personaError } = await this.databaseService
      .getClient()
      .from('personas')
      .select('id, usuario_id')
      .eq('id', personaId)
      .single();

    if (personaError || !persona) {
      throw new NotFoundException('Person not found');
    }

    if (persona.usuario_id) {
      throw new BadRequestException('Person already has a linked user account');
    }

    // Verify user exists and doesn't already have a linked person
    const { data: usuario, error: usuarioError } = await this.databaseService
      .getClient()
      .from('usuarios')
      .select('id')
      .eq('id', vincularDto.usuario_id)
      .single();

    if (usuarioError || !usuario) {
      throw new NotFoundException('User not found');
    }

    // Check if user is already linked to another person
    const { data: existingLink, error: linkError } = await this.databaseService
      .getClient()
      .from('personas')
      .select('id')
      .eq('usuario_id', vincularDto.usuario_id)
      .single();

    if (!linkError && existingLink) {
      throw new BadRequestException('User is already linked to another person');
    }

    // Update person with user_id
    const { error: updateError } = await this.databaseService
      .getClient()
      .from('personas')
      .update({ usuario_id: vincularDto.usuario_id })
      .eq('id', personaId);

    if (updateError) {
      throw new BadRequestException(
        `Error linking user to person: ${updateError.message}`,
      );
    }

    return {
      message: 'Usuario vinculado exitosamente a la persona',
      persona_id: personaId,
      usuario_id: vincularDto.usuario_id,
    };
  }

  /**
   * Create a new sprint
   */
  async createSprint(
    createSprintDto: CreateSprintDto,
  ): Promise<CreateSprintResponseDto> {
    // Verify company exists
    const { data: empresa, error: empresaError } = await this.databaseService
      .getClient()
      .from('empresas')
      .select('id')
      .eq('id', createSprintDto.empresa_id)
      .single();

    if (empresaError || !empresa) {
      throw new NotFoundException('Company not found');
    }

    // Create sprint
    const { data: sprint, error: sprintError } = await this.databaseService
      .getClient()
      .from('sprints_empresas')
      .insert({
        empresa_id: createSprintDto.empresa_id,
        titulo: createSprintDto.titulo,
        fecha_inicio: createSprintDto.fecha_inicio,
        fecha_fin: createSprintDto.fecha_fin,
        estado: 'planificada',
      })
      .select()
      .single();

    if (sprintError || !sprint) {
      throw new BadRequestException(
        `Error creating sprint: ${sprintError?.message}`,
      );
    }

    return {
      id: sprint.id,
      titulo: sprint.titulo,
      fecha_inicio: sprint.fecha_inicio,
      fecha_fin: sprint.fecha_fin,
      estado: sprint.estado,
      message: 'Sprint creado exitosamente',
    };
  }

  /**
   * Create manual challenges for multiple users
   */
  async createRetoManual(
    createRetoDto: CreateRetoManualDto,
  ): Promise<CreateRetoManualResponseDto> {
    const retoIds: string[] = [];

    // Create challenges for each user
    for (const usuarioId of createRetoDto.usuario_ids) {
      // Verify user exists
      const { data: usuario, error: usuarioError } = await this.databaseService
        .getClient()
        .from('usuarios')
        .select('id')
        .eq('id', usuarioId)
        .single();

      if (usuarioError || !usuario) {
        throw new NotFoundException(`User not found: ${usuarioId}`);
      }

      // Create main challenge
      const { data: reto, error: retoError } = await this.databaseService
        .getClient()
        .from('retos_usuarios')
        .insert({
          usuario_id: usuarioId,
          titulo: createRetoDto.titulo,
          descripcion: createRetoDto.descripcion,
          puntos_recompensa: createRetoDto.puntos_recompensa,
          estado: 'pendiente',
          prioridad: 1,
          url_destino: this.getUrlDestinoForTipoAccion(
            createRetoDto.tipo_accion,
          ),
        })
        .select()
        .single();

      if (retoError || !reto) {
        throw new BadRequestException(
          `Error creating challenge for user ${usuarioId}: ${retoError?.message}`,
        );
      }

      retoIds.push(reto.id);

      // Create subtask if needed
      if (
        createRetoDto.entidad_relacionada_id &&
        createRetoDto.entidad_relacionada_tipo
      ) {
        const { error: subtareaError } = await this.databaseService
          .getClient()
          .from('retos_subtareas')
          .insert({
            reto_usuario_id: reto.id,
            titulo: createRetoDto.titulo,
            descripcion: createRetoDto.descripcion,
            tipo_accion: createRetoDto.tipo_accion,
            entidad_relacionada_id: createRetoDto.entidad_relacionada_id,
            entidad_relacionada_tipo: createRetoDto.entidad_relacionada_tipo,
            estado: 'pendiente',
          });

        if (subtareaError) {
          throw new BadRequestException(
            `Error creating subtask for challenge ${reto.id}: ${subtareaError.message}`,
          );
        }
      }
    }

    return {
      retos_creados: retoIds.length,
      reto_ids: retoIds,
      message: 'Retos creados y asignados exitosamente',
    };
  }

  /**
   * Get detailed information about a person
   */
  async getPersonaDetalle(
    personaId: string,
  ): Promise<PersonaDetalleResponseDto> {
    // Get person basic info
    const { data: persona, error: personaError } = await this.databaseService
      .getClient()
      .from('personas')
      .select(
        `
        id,
        nombre,
        apellidos,
        cargo,
        email,
        telefono,
        usuario_id,
        departamentos(nombre)
      `,
      )
      .eq('id', personaId)
      .single();

    if (personaError || !persona) {
      throw new NotFoundException('Person not found');
    }

    // Get user info if linked
    let usuario = undefined;
    if (persona.usuario_id) {
      const { data: userData, error: userError } = await this.databaseService
        .getClient()
        .from('usuarios')
        .select('id, email, rol, puntos, racha_actual, ultimo_acceso')
        .eq('id', persona.usuario_id)
        .single();

      if (!userError && userData) {
        usuario = userData;
      }
    }

    // Get KPIs
    const kpis = await this.getPersonaKpis(personaId);

    // Get processes
    const procesos = await this.getPersonaProcesos(personaId);

    // Get findings
    const hallazgos = await this.getPersonaHallazgos(personaId);

    return {
      persona: {
        id: persona.id,
        nombre_completo: `${persona.nombre} ${persona.apellidos}`,
        cargo: persona.cargo,
        departamento: (persona.departamentos as any)?.nombre || 'Sin departamento',
        email: persona.email,
        telefono: persona.telefono,
      },
      usuario,
      kpis,
      procesos,
      hallazgos,
    };
  }

  // Private helper methods
  private async getActiveSprint(
    empresaId: string,
  ): Promise<SprintDataDto | undefined> {
    const { data: sprint, error } = await this.databaseService
      .getClient()
      .from('sprints_empresas')
      .select('*')
      .eq('empresa_id', empresaId)
      .eq('estado', 'activa')
      .single();

    if (error || !sprint) {
      return undefined;
    }

    return this.formatSprintData(sprint);
  }

  private async getSprintHistory(empresaId: string): Promise<SprintDataDto[]> {
    const { data: sprints, error } = await this.databaseService
      .getClient()
      .from('sprints_empresas')
      .select('*')
      .eq('empresa_id', empresaId)
      .order('fecha_inicio', { ascending: false });

    if (error || !sprints) {
      return [];
    }

    return Promise.all(sprints.map((sprint) => this.formatSprintData(sprint)));
  }

  private async formatSprintData(sprint: any): Promise<SprintDataDto> {
    const fechaInicio = new Date(sprint.fecha_inicio);
    const fechaFin = new Date(sprint.fecha_fin);
    const hoy = new Date();

    const diasTotales = Math.ceil(
      (fechaFin.getTime() - fechaInicio.getTime()) / (1000 * 60 * 60 * 24),
    );
    const diasTranscurridos = Math.max(
      0,
      Math.ceil(
        (hoy.getTime() - fechaInicio.getTime()) / (1000 * 60 * 60 * 24),
      ),
    );

    // Get challenge counts for this sprint period
    const { data: retos, error } = await this.databaseService
      .getClient()
      .from('retos_usuarios')
      .select('estado')
      .gte('created_at', sprint.fecha_inicio)
      .lte('created_at', sprint.fecha_fin);

    const retosCompletados =
      retos?.filter((r) => r.estado === 'completado').length || 0;
    const retosPendientes =
      retos?.filter((r) => r.estado === 'pendiente').length || 0;

    return {
      id: sprint.id,
      titulo: sprint.titulo,
      fecha_inicio: sprint.fecha_inicio,
      fecha_fin: sprint.fecha_fin,
      estado: sprint.estado,
      dias_transcurridos: diasTranscurridos,
      dias_totales: diasTotales,
      retos_completados: retosCompletados,
      retos_pendientes: retosPendientes,
    };
  }

  private async getUsuariosPersonas(
    empresaId: string,
  ): Promise<UsuarioPersonaDto[]> {
    const { data: usuariosPersonas, error } = await this.databaseService
      .getClient()
      .from('usuarios')
      .select(
        `
        id,
        puntos,
        numero_retos_diario,
        rol,
        personas!inner(
          id,
          nombre,
          apellidos,
          cargo,
          departamentos(nombre)
        )
      `,
      )
      .eq('empresa_id', empresaId);

    if (error || !usuariosPersonas) {
      return [];
    }

    const result: UsuarioPersonaDto[] = [];

    for (const up of usuariosPersonas) {
      const persona = (up as any).personas;

      // Get completed challenges count
      const { data: retosCompletados, error: retosError } =
        await this.databaseService
          .getClient()
          .from('retos_usuarios')
          .select('id')
          .eq('usuario_id', up.id)
          .eq('estado', 'completado');

      // Get assigned processes count
      const { data: procesosAsignados, error: procesosError } =
        await this.databaseService
          .getClient()
          .from('procesos_clientes_responsables')
          .select('id')
          .eq('persona_cliente_id', persona.id);

      // Get reported findings count
      const { data: hallazgosReportados, error: hallazgosError } =
        await this.databaseService
          .getClient()
          .from('hallazgos_clientes')
          .select('id')
          .eq('persona_id', persona.id);

      result.push({
        usuario_id: up.id,
        persona_id: persona.id,
        nombre_completo: `${persona.nombre} ${persona.apellidos}`,
        cargo: persona.cargo,
        departamento: (persona.departamentos as any)?.nombre || 'Sin departamento',
        rol: up.rol,
        puntos_totales: up.puntos,
        retos_completados: retosCompletados?.length || 0,
        numero_retos_diario: up.numero_retos_diario,
        procesos_asignados: procesosAsignados?.length || 0,
        hallazgos_reportados: hallazgosReportados?.length || 0,
      });
    }

    return result;
  }

  private async getPersonasSinUsuario(empresaId: string): Promise<
    Array<{
      id: string;
      nombre_completo: string;
      cargo: string;
      departamento: string;
    }>
  > {
    const { data: personas, error } = await this.databaseService
      .getClient()
      .from('personas')
      .select(
        `
        id,
        nombre,
        apellidos,
        cargo,
        departamentos(nombre)
      `,
      )
      .eq('empresa_id', empresaId)
      .is('usuario_id', null)
      .eq('activo', true);

    if (error || !personas) {
      return [];
    }

    return personas.map((persona) => ({
      id: persona.id,
      nombre_completo: `${persona.nombre} ${persona.apellidos}`,
      cargo: persona.cargo,
      departamento: (persona.departamentos as any)?.nombre || 'Sin departamento',
    }));
  }

  private async getPersonaKpis(personaId: string): Promise<PersonaKpiDto> {
    // Get assigned processes count
    const { data: procesosAsignados, error: procesosError } =
      await this.databaseService
        .getClient()
        .from('procesos_clientes_responsables')
        .select('id')
        .eq('persona_cliente_id', personaId);

    // Get reported findings count
    const { data: hallazgosReportados, error: hallazgosError } =
      await this.databaseService
        .getClient()
        .from('hallazgos_clientes')
        .select('id')
        .eq('persona_id', personaId);

    // Get user data for challenges and points
    const { data: persona, error: personaError } = await this.databaseService
      .getClient()
      .from('personas')
      .select('usuario_id')
      .eq('id', personaId)
      .single();

    let retosCompletados = 0;
    let puntosAcumulados = 0;

    if (!personaError && persona?.usuario_id) {
      const { data: usuario, error: usuarioError } = await this.databaseService
        .getClient()
        .from('usuarios')
        .select('puntos')
        .eq('id', persona.usuario_id)
        .single();

      if (!usuarioError && usuario) {
        puntosAcumulados = usuario.puntos;
      }

      const { data: retos, error: retosError } = await this.databaseService
        .getClient()
        .from('retos_usuarios')
        .select('id')
        .eq('usuario_id', persona.usuario_id)
        .eq('estado', 'completado');

      if (!retosError && retos) {
        retosCompletados = retos.length;
      }
    }

    return {
      procesos_asignados: procesosAsignados?.length || 0,
      hallazgos_reportados: hallazgosReportados?.length || 0,
      retos_completados: retosCompletados,
      puntos_acumulados: puntosAcumulados,
    };
  }

  private async getPersonaProcesos(
    personaId: string,
  ): Promise<ProcesoPersonaDto[]> {
    const { data: procesos, error } = await this.databaseService
      .getClient()
      .from('procesos_clientes_responsables')
      .select(
        `
        procesos_clientes(
          id,
          nombre,
          descripcion,
          estado_analisis,
          duracion_minutos_por_ejecucion,
          es_repetitivo
        )
      `,
      )
      .eq('persona_cliente_id', personaId);

    if (error || !procesos) {
      return [];
    }

    return procesos.map((p: any) => ({
      id: p.procesos_clientes.id,
      nombre: p.procesos_clientes.nombre,
      descripcion: p.procesos_clientes.descripcion,
      estado_analisis: p.procesos_clientes.estado_analisis,
      duracion_minutos_por_ejecucion:
        p.procesos_clientes.duracion_minutos_por_ejecucion,
      es_repetitivo: p.procesos_clientes.es_repetitivo,
    }));
  }

  private async getPersonaHallazgos(
    personaId: string,
  ): Promise<HallazgoPersonaDto[]> {
    const { data: hallazgos, error } = await this.databaseService
      .getClient()
      .from('hallazgos_clientes')
      .select('id, titulo, tipo, descripcion, impacto, estado, created_at')
      .eq('persona_id', personaId)
      .order('created_at', { ascending: false });

    if (error || !hallazgos) {
      return [];
    }

    return hallazgos.map((h) => ({
      id: h.id,
      titulo: h.titulo,
      tipo: h.tipo,
      descripcion: h.descripcion,
      impacto: h.impacto,
      estado: h.estado,
      created_at: h.created_at,
    }));
  }

  private getUrlDestinoForTipoAccion(tipoAccion: string): string {
    switch (tipoAccion) {
      case 'definir_proceso':
        return '/portal/definir-proceso';
      case 'responder_pregunta':
        return '/portal/responder-pregunta';
      case 'definir_tarea':
        return '/portal/definir-tarea';
      case 'definir_duracion_proceso':
        return '/portal/definir-duracion';
      default:
        return '/portal/dashboard';
    }
  }
}
