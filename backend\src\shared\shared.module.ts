import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/common/database.module';
import { GamificationService } from './gamification.service';
import { N8NWebhookService } from './n8n-webhook.service';
import { StorageService } from './storage.service';

@Module({
  imports: [DatabaseModule, ConfigModule],
  providers: [N8NWebhookService, StorageService, GamificationService],
  exports: [
    N8NWebhookService,
    StorageService,
    GamificationService,
    DatabaseModule,
  ],
})
export class SharedModule {}
