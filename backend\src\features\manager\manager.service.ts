/* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call */
import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../common/database.service';
import { Usuarios } from '../../common/database.types';
import {
  DashboardSummaryDto,
  KpiDataDto,
  ChartDataPointDto,
} from './dto/dashboard-summary.dto';
import {
  ProcessListQueryDto,
  ProcessListResponseDto,
  ProcessListItemDto,
} from './dto/process-list.dto';
import {
  FindingListQueryDto,
  FindingListResponseDto,
  FindingListItemDto,
} from './dto/finding-list.dto';
import { ProcessDetailDto } from './dto/process-detail.dto';
import { FindingDetailDto } from './dto/finding-detail.dto';

/**
 * Service for manager dashboard and analytics functionality
 * Provides aggregated data and insights for company managers
 */
@Injectable()
export class ManagerService {
  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * Get comprehensive dashboard summary for managers
   * Includes KPIs, charts, and recent activity
   */
  async getDashboardSummary(user: Usuarios): Promise<DashboardSummaryDto> {
    if (!user.empresa_id) {
      throw new Error('User does not have an associated company');
    }

    // Get KPIs
    const kpis = await this.getKpis(user.empresa_id);

    // Get chart data
    const processesByDepartment = await this.getProcessesByDepartment(
      user.empresa_id,
    );
    const findingsByType = await this.getFindingsByType(user.empresa_id);
    const recentActivity = await this.getRecentActivity(user.empresa_id);

    // Get top performers
    const topPerformers = await this.getTopPerformers(user.empresa_id);

    // Get manager's personal challenge if exists
    const managerChallenge = await this.getManagerChallenge(user.id);

    return {
      kpis,
      processesByDepartment,
      findingsByType,
      recentActivity,
      topPerformers,
      managerChallenge: managerChallenge || undefined,
    };
  }

  /**
   * Get paginated list of processes with filtering
   */
  async getProcesses(
    user: Usuarios,
    query: ProcessListQueryDto,
  ): Promise<ProcessListResponseDto> {
    const client = this.databaseService.getClient();
    const {
      page = 1,
      limit = 10,
      departamento,
      search,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = query;
    const offset = (page - 1) * limit;

    if (!user.empresa_id) {
      throw new Error('User does not have an associated company');
    }

    let queryBuilder = client
      .from('procesos_clientes')
      .select(
        `
        id,
        nombre_proceso,
        descripcion_breve,
        duracion_minutos,
        frecuencia,
        created_at,
        departamentos!inner(nombre),
        procesos_clientes_responsables(
          personas(nombre, apellidos)
        )
      `,
      )
      .eq('empresa_cliente_id', user.empresa_id);

    // Apply filters
    if (departamento) {
      queryBuilder = queryBuilder.eq('departamentos.nombre', departamento);
    }

    if (search) {
      queryBuilder = queryBuilder.or(
        `nombre_proceso.ilike.%${search}%,descripcion_breve.ilike.%${search}%`,
      );
    }

    // Apply sorting
    queryBuilder = queryBuilder.order(sortBy, {
      ascending: sortOrder === 'asc',
    });

    // Get total count for pagination
    const countQuery = await client
      .from('procesos_clientes')
      .select('id', { count: 'exact', head: true })
      .eq('empresa_cliente_id', user.empresa_id);

    const total = countQuery.count || 0;

    // Get paginated data
    const { data: processes, error } = await queryBuilder.range(
      offset,
      offset + limit - 1,
    );

    if (error) {
      throw new Error(`Failed to fetch processes: ${error.message}`);
    }

    // Transform data
    const transformedData: ProcessListItemDto[] = await Promise.all(
      processes.map(async (process: any) => {
        // Get task count
        const { count: taskCount } = await client
          .from('tareas_clientes')
          .select('id', { count: 'exact', head: true })
          .eq('proceso_cliente_id', process.id);

        // Get findings count
        const { count: findingsCount } = await client
          .from('hallazgos_clientes')
          .select('id', { count: 'exact', head: true })
          .eq('proceso_relacionado_id', process.id);

        const departamento = process.departamentos?.[0]?.nombre || 'N/A';
        const responsable = process.procesos_clientes_responsables?.[0]
          ?.personas
          ? `${process.procesos_clientes_responsables[0].personas.nombre} ${process.procesos_clientes_responsables[0].personas.apellidos}`
          : 'N/A';

        return {
          id: process.id,
          nombre_proceso: process.nombre_proceso,
          descripcion_breve: process.descripcion_breve,
          departamento,
          responsable,
          duracion_minutos: process.duracion_minutos,
          frecuencia: process.frecuencia,
          created_at: process.created_at,
          total_tareas: taskCount || 0,
          total_hallazgos: findingsCount || 0,
        };
      }),
    );

    return {
      data: transformedData,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get detailed information for a specific process
   */
  async getProcessDetail(
    user: Usuarios,
    processId: string,
  ): Promise<ProcessDetailDto> {
    const client = this.databaseService.getClient();

    if (!user.empresa_id) {
      throw new Error('User does not have an associated company');
    }

    const { data: process, error } = await client
      .from('procesos_clientes')
      .select(
        `
        *,
        departamentos!inner(nombre),
        tareas_clientes(*),
        procesos_clientes_responsables(
          personas(*)
        ),
        procesos_fuentes_informacion(
          *,
          personas(nombre, apellidos)
        )
      `,
      )
      .eq('id', processId)
      .eq('empresa_cliente_id', user.empresa_id)
      .single();

    if (error || !process) {
      throw new Error('Process not found');
    }

    // Get findings count
    const { count: findingsCount } = await client
      .from('hallazgos_clientes')
      .select('id', { count: 'exact', head: true })
      .eq('proceso_relacionado_id', processId);

    return {
      id: process.id,
      nombre_proceso: process.nombre_proceso,
      descripcion_breve: process.descripcion_breve,
      descripcion_detallada: process.descripcion_detallada || '',
      departamento: process.departamentos?.[0]?.nombre || 'N/A',
      duracion_minutos: process.duracion_minutos,
      frecuencia: process.frecuencia,
      complejidad: process.complejidad,
      potencial_automatizacion: process.potencial_automatizacion,
      created_at: process.created_at,
      updated_at: process.updated_at,
      tareas:
        process.tareas_clientes?.map((task: any) => ({
          id: task.id,
          nombre_tarea: task.nombre_tarea,
          descripcion: task.descripcion,
          duracion_minutos: task.duracion_minutos,
          orden: task.orden,
          responsable: task.responsable_nombre,
        })) || [],
      responsables:
        process.procesos_clientes_responsables?.map((resp: any) => ({
          id: resp.personas.id,
          nombre: resp.personas.nombre,
          apellidos: resp.personas.apellidos,
          cargo: resp.personas.cargo,
          departamento: resp.personas.departamento?.nombre || 'N/A',
          email: resp.personas.email,
        })) || [],
      fuentes_informacion:
        process.procesos_fuentes_informacion?.map((source: any) => ({
          id: source.id,
          nombre_informacion: source.nombre_informacion,
          descripcion: source.descripcion,
          formato: source.formato,
          persona_responsable: `${source.personas.nombre} ${source.personas.apellidos}`,
          url_adjunto: source.url_adjunto,
        })) || [],
      total_hallazgos: findingsCount || 0,
    };
  }

  // Private helper methods for dashboard data
  private async getKpis(empresaId: string): Promise<KpiDataDto[]> {
    const client = this.databaseService.getClient();

    // Get processes count
    const { count: processesCount } = await client
      .from('procesos_clientes')
      .select('id', { count: 'exact', head: true })
      .eq('empresa_cliente_id', empresaId);

    // Get findings count
    const { count: findingsCount } = await client
      .from('hallazgos_clientes')
      .select('id', { count: 'exact', head: true });

    // Get active users count
    const { count: activeUsersCount } = await client
      .from('usuarios')
      .select('id', { count: 'exact', head: true })
      .eq('empresa_id', empresaId)
      .eq('activo', true);

    // Get completed challenges count
    const { count: completedChallenges } = await client
      .from('retos_usuarios')
      .select('id', { count: 'exact', head: true })
      .eq('estado', 'completado');

    return [
      {
        title: 'Procesos Identificados',
        value: processesCount || 0,
        trend: 'up',
      },
      {
        title: 'Hallazgos Detectados',
        value: findingsCount || 0,
        trend: 'up',
      },
      {
        title: 'Usuarios Activos',
        value: activeUsersCount || 0,
        trend: 'stable',
      },
      {
        title: 'Retos Completados',
        value: completedChallenges || 0,
        trend: 'up',
      },
    ];
  }

  private async getProcessesByDepartment(
    empresaId: string,
  ): Promise<ChartDataPointDto[]> {
    const client = this.databaseService.getClient();

    const { data, error } = await client
      .from('procesos_clientes')
      .select(
        `
        departamentos!inner(nombre)
      `,
      )
      .eq('empresa_cliente_id', empresaId);

    if (error || !data) return [];

    // Group by department
    const departmentCounts = data.reduce(
      (acc, process: any) => {
        const deptName =
          process.departamentos?.[0]?.nombre || 'Sin Departamento';
        acc[deptName] = (acc[deptName] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return Object.entries(departmentCounts).map(([label, value]) => ({
      label,
      value,
      color: this.getRandomColor(),
    }));
  }

  private async getFindingsByType(
    empresaId: string,
  ): Promise<ChartDataPointDto[]> {
    const client = this.databaseService.getClient();

    const { data, error } = await client
      .from('hallazgos_clientes')
      .select('tipo');

    if (error || !data) return [];

    // Group by type
    const typeCounts = data.reduce(
      (acc, finding) => {
        const type = finding.tipo || 'Sin Tipo';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return Object.entries(typeCounts).map(([label, value]) => ({
      label,
      value,
      color: this.getRandomColor(),
    }));
  }

  private getRecentActivity(_empresaId: string): ChartDataPointDto[] {
    // This would typically show activity over time
    // For now, return mock data
    return [
      { label: 'Lun', value: 12 },
      { label: 'Mar', value: 19 },
      { label: 'Mié', value: 8 },
      { label: 'Jue', value: 15 },
      { label: 'Vie', value: 22 },
      { label: 'Sáb', value: 5 },
      { label: 'Dom', value: 3 },
    ];
  }

  private async getTopPerformers(empresaId: string) {
    const client = this.databaseService.getClient();

    const { data, error } = await client
      .from('usuarios')
      .select(
        `
        id,
        nombre,
        puntos,
        personas!inner(departamentos(nombre))
      `,
      )
      .eq('empresa_id', empresaId)
      .eq('activo', true)
      .order('puntos', { ascending: false })
      .limit(5);

    if (error || !data) return [];

    return data.map((user: any) => ({
      id: user.id,
      nombre: user.nombre,
      puntos: user.puntos,
      departamento: user.personas?.[0]?.departamentos?.[0]?.nombre || 'N/A',
    }));
  }

  private async getManagerChallenge(userId: string) {
    const client = this.databaseService.getClient();

    const { data, error } = await client
      .from('retos_usuarios')
      .select('*')
      .eq('usuario_id', userId)
      .eq('estado', 'pendiente')
      .limit(1)
      .single();

    if (error || !data) return null;

    return {
      id: data.id,
      titulo: data.titulo,
      descripcion: data.descripcion,
      puntos_recompensa: data.puntos_recompensa,
      estado: data.estado,
    };
  }

  /**
   * Get paginated list of findings with filtering
   */
  async getFindings(
    user: Usuarios,
    query: FindingListQueryDto,
  ): Promise<FindingListResponseDto> {
    const client = this.databaseService.getClient();
    const {
      page = 1,
      limit = 10,
      tipo,
      departamento,
      search,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = query;
    const offset = (page - 1) * limit;

    let queryBuilder = client.from('hallazgos_clientes').select(`
        id,
        titulo,
        descripcion,
        tipo,
        nivel_impacto,
        prioridad,
        created_at,
        proceso_relacionado_id,
        procesos_clientes!left(nombre_proceso, departamentos!inner(nombre)),
        identificado_por_usuario_id,
        usuarios!inner(nombre, apellidos)
      `);

    // Apply filters
    if (tipo) {
      queryBuilder = queryBuilder.eq('tipo', tipo);
    }

    if (departamento) {
      queryBuilder = queryBuilder.eq(
        'procesos_clientes.departamentos.nombre',
        departamento,
      );
    }

    if (search) {
      queryBuilder = queryBuilder.or(
        `titulo.ilike.%${search}%,descripcion.ilike.%${search}%`,
      );
    }

    // Apply sorting
    queryBuilder = queryBuilder.order(sortBy, {
      ascending: sortOrder === 'asc',
    });

    // Get total count for pagination
    const countQuery = await client
      .from('hallazgos_clientes')
      .select('id', { count: 'exact', head: true });

    const total = countQuery.count || 0;

    // Get paginated data
    const { data: findings, error } = await queryBuilder.range(
      offset,
      offset + limit - 1,
    );

    if (error) {
      throw new Error(`Failed to fetch findings: ${error.message}`);
    }

    // Transform data
    const transformedData: FindingListItemDto[] = findings.map(
      (finding: any) => ({
        id: finding.id,
        titulo: finding.titulo,
        descripcion: finding.descripcion,
        tipo: finding.tipo,
        departamento:
          finding.procesos_clientes?.[0]?.departamentos?.[0]?.nombre || 'N/A',
        proceso_relacionado: finding.procesos_clientes?.[0]?.nombre_proceso,
        nivel_impacto: finding.nivel_impacto,
        prioridad: finding.prioridad,
        created_at: finding.created_at,
        identificado_por:
          `${finding.usuarios?.[0]?.nombre || ''} ${finding.usuarios?.[0]?.apellidos || ''}`.trim(),
      }),
    );

    return {
      data: transformedData,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get detailed information for a specific finding
   */
  async getFindingDetail(
    user: Usuarios,
    findingId: string,
  ): Promise<FindingDetailDto> {
    const client = this.databaseService.getClient();

    const { data: finding, error } = await client
      .from('hallazgos_clientes')
      .select(
        `
        *,
        procesos_clientes(id, nombre_proceso, departamentos(nombre)),
        usuarios!inner(id, nombre, apellidos, personas(cargo, departamentos(nombre)))
      `,
      )
      .eq('id', findingId)
      .single();

    if (error || !finding) {
      throw new Error('Finding not found');
    }

    return {
      id: finding.id,
      titulo: finding.titulo,
      descripcion: finding.descripcion,
      descripcion_detallada: finding.descripcion_detallada || '',
      tipo: finding.tipo,
      departamento: finding.procesos_clientes?.departamentos?.nombre || 'N/A',
      proceso_relacionado: finding.procesos_clientes
        ? {
            id: finding.procesos_clientes.id,
            nombre_proceso: finding.procesos_clientes.nombre_proceso,
          }
        : undefined,
      nivel_impacto: finding.nivel_impacto,
      prioridad: finding.prioridad,
      tiempo_ahorro_estimado: finding.tiempo_ahorro_estimado,
      esfuerzo_implementacion: finding.esfuerzo_implementacion,
      roi_potencial: finding.roi_potencial,
      estado: finding.estado,
      identificado_por: {
        id: finding.usuarios.id,
        nombre: finding.usuarios.nombre,
        apellidos: finding.usuarios.apellidos,
        cargo: finding.usuarios.personas?.cargo || 'N/A',
        departamento: finding.usuarios.personas?.departamentos?.nombre || 'N/A',
      },
      created_at: finding.created_at,
      updated_at: finding.updated_at,
      info_adicional: finding.info_adicional,
      acciones_recomendadas: finding.acciones_recomendadas,
    };
  }

  private getRandomColor(): string {
    const colors = [
      '#3B82F6',
      '#EF4444',
      '#10B981',
      '#F59E0B',
      '#8B5CF6',
      '#EC4899',
      '#06B6D4',
      '#84CC16',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}
