import { useAuthStore } from '@/features/auth/store/useAuthStore';
import { useGetDashboard } from '../hooks/useGetDashboard';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { RetoCard } from './RetoCard';
import { Link } from 'react-router-dom';

export const DashboardLayout = () => {
  const user = useAuthStore((state) => state.user);
  const { data: dashboardData, isLoading, isError } = useGetDashboard();

  return (
    <div className="p-4 md:p-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">
          ¡Ho<PERSON>, {user?.nombre || 'Usuario'}!
        </h1>
        <p className="text-gray-500">
          Bienvenido a tu portal. Aquí tienes un resumen de tu progreso.
        </p>
      </header>

      {/* Navigation Menu */}
      <nav className="mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Navegación</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

            {/* Dashboard Link */}
            <Link
              to="/inicio"
              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">🏠</span>
              <div>
                <h4 className="font-medium text-gray-900">Dashboard</h4>
                <p className="text-sm text-gray-500">Panel principal</p>
              </div>
            </Link>

            {/* Ranking Link */}
            <Link
              to="/portal/ranking"
              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">🏆</span>
              <div>
                <h4 className="font-medium text-gray-900">Ranking</h4>
                <p className="text-sm text-gray-500">Clasificación de usuarios</p>
              </div>
            </Link>

            {/* All Challenges Link */}
            <Link
              to="/portal/todos-los-retos"
              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">🎯</span>
              <div>
                <h4 className="font-medium text-gray-900">Todos los Retos</h4>
                <p className="text-sm text-gray-500">Ver todos los retos</p>
              </div>
            </Link>

            {/* Manager Dashboard - Only for cliente_gerente */}
            {user?.rol === 'cliente_gerente' && (
              <Link
                to="/portal/dashboard"
                className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <span className="text-2xl mr-3">📊</span>
                <div>
                  <h4 className="font-medium text-gray-900">Dashboard Gerencial</h4>
                  <p className="text-sm text-gray-500">Panel de manager</p>
                </div>
              </Link>
            )}

            {/* Admin Panel - Only for admin_aceleralia */}
            {user?.rol === 'admin_aceleralia' && (
              <Link
                to="/admin/gestor"
                className="flex items-center p-3 rounded-lg border border-blue-300 bg-blue-50 hover:bg-blue-100 transition-colors"
              >
                <span className="text-2xl mr-3">⚙️</span>
                <div>
                  <h4 className="font-medium text-blue-900">Panel de Administración</h4>
                  <p className="text-sm text-blue-600">Gestión de Aceleralia</p>
                </div>
              </Link>
            )}

          </div>
        </div>
      </nav>

      {isLoading && <LoadingSpinner />}
      {isError && <p>Error al cargar el dashboard.</p>}

      {dashboardData && (
        <div>
          {/* Placeholder for Sprint Progress */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-2">Progreso del Sprint</h2>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div
                className="bg-blue-600 h-4 rounded-full"
                style={{ width: `${dashboardData.sprint.progressPercentage}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-sm text-gray-600 mt-1">
              <span>{dashboardData.sprint.title}</span>
              <span>{dashboardData.sprint.progressPercentage}%</span>
            </div>
          </div>

          {/* Placeholder for KPIs */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="p-4 bg-white rounded-lg shadow">
              <h3 className="font-semibold text-gray-500">Puntos</h3>
              <p className="text-3xl font-bold">{dashboardData.kpis.points}</p>
            </div>
            <div className="p-4 bg-white rounded-lg shadow">
              <h3 className="font-semibold text-gray-500">Racha</h3>
              <p className="text-3xl font-bold">{dashboardData.kpis.streak} días</p>
            </div>
            <div className="p-4 bg-white rounded-lg shadow">
              <h3 className="font-semibold text-gray-500">Retos Completados</h3>
              <p className="text-3xl font-bold">{dashboardData.kpis.completedChallenges}</p>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-2">Retos Diarios</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dashboardData.dailyChallenges.map((reto) => (
                <RetoCard key={reto.id} reto={reto} />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};