import { Test, TestingModule } from '@nestjs/testing';
import { GamificationService } from './gamification.service';
import { DatabaseService } from '../common/database.service';
import { ConfigService } from '@nestjs/config';

describe('GamificationService', () => {
  let service: GamificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GamificationService,
        {
          provide: DatabaseService,
          useValue: {
            getClient: jest.fn().mockReturnValue({
              from: jest.fn().mockReturnThis(),
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: {}, error: null }),
              update: jest.fn().mockResolvedValue({ error: null }),
            }),
          },
        },
        ConfigService,
      ],
    }).compile();

    service = module.get<GamificationService>(GamificationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
