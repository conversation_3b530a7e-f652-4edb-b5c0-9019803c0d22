# Project Setup Guide

This document describes **all the steps required to get a local development environment up and running** for the Aceleralia Customer Portal monorepo.  
Follow the guide in order—skipping steps can lead to confusing errors.

> **NOTE**  
> If you encounter a problem that is not covered here, please add the solution to this guide under the _Troubleshooting_ section so the team benefits from your findings.

---

## 1&nbsp;&nbsp;Repository Layout

```
portal_clientes/
├─ backend/         NestJS REST API and shared services
├─ frontend/        React (Vite) web application
└─ docs/            Architecture, features and sprint documentation
```

---

## 2&nbsp;&nbsp;Prerequisites

| Tool | Version | Reason / Usage |
|------|---------|----------------|
| Node.js | **≥ 20.x** | Runtime for both backend & tooling |
| NPM   | **≥ 10.x** (ships with Node 20) | Package manager |
| PostgreSQL or Supabase instance | 15+ | Primary database |
| `pnpm` *(optional)* | latest | Faster dependency installs |
| Git | latest | Source-control |

Make sure `node -v` returns **20.x** or higher.  
We intentionally target LTS to avoid unexpected language-level issues.

---

## 3&nbsp;&nbsp;Cloning & Bootstrapping

```bash
git clone https://github.com/tu-organizacion/portal_clientes.git
cd portal_clientes
```

### 3.1&nbsp;&nbsp;Install Dependencies

Install **once** at the repo root; workspaces will route packages correctly:

```bash
npm install            # or: pnpm install
```

---

## 4&nbsp;&nbsp;Environment Variables

Copy the example file and fill in the blanks:

```bash
cp backend/.env.example backend/.env
```

| Variable | Description |
|----------|-------------|
| `SUPABASE_URL` | Base URL of your Supabase project |
| `SUPABASE_ANON_KEY` | Public anon key (frontend) |
| `SUPABASE_SERVICE_ROLE_KEY` | Service-role key (backend privileged queries) |
| `DATABASE_URL` | Full Postgres connection string **including password**<br>`**********************************/db` |
| `DB_HOST` / `DB_PORT` / etc. | Optional split variables; used by legacy tests |

> **Security**  
> Do **not** commit real credentials. `.env*` is git-ignored.

---

## 5&nbsp;&nbsp;Generating Type-Safe Database Types

We rely on [`pg-to-ts`](https://github.com/ajborson/pg-to-ts) instead of the Supabase CLI to avoid the *SUPABASE_ACCESS_TOKEN* requirement.

### 5.1&nbsp;&nbsp;One-Off Generation

```bash
cd backend
npm run generate-types
```

The script is declared in [`backend/package.json`](../backend/package.json):  

```json
"generate-types": "pg-to-ts generate -c $DATABASE_URL -s public -o src/common/database.types.ts"
```

### 5.2&nbsp;&nbsp;When to Regenerate

* After **any** DDL change (new table, column, enum).
* After pulling from remote if someone else changed the schema.
* Before committing if your PR adds migrations.

---

## 6&nbsp;&nbsp;Local Development Startup
### 6.1&nbsp;&nbsp;Running the Backend

```bash
cd backend
npm run start:dev      # hot-reload server on http://localhost:3000
```

Logs should include:

```
[Nest] ...  Supabase client initialized.
```

If you see a connection error, double-check `DATABASE_URL` and that the database is reachable.

---

### 6.2&nbsp;&nbsp;Running the Frontend

```bash
cd frontend
npm run dev            # Vite dev server on http://localhost:5173
```

The app will proxy API requests to `localhost:3000` by default.  
Configure proxy settings in `frontend/vite.config.ts` if your backend runs elsewhere.

---

### 6.3&nbsp;&nbsp;Summary: Running the Full Application
1.  **Terminal 1 (Backend):**
    ```bash
    cd backend
    npm run start:dev
    ```
2.  **Terminal 2 (Frontend):**
    ```bash
    cd frontend
    npm run dev
    ```
3.  **Open your browser** to `http://localhost:5173`.
4.  **API Documentation** is available at `http://localhost:3000/api` (Swagger UI).
5.  **Regenerate types** whenever the database schema changes:
    ```bash
    cd backend
    npm run generate-types
    ```
## 7&nbsp;&nbsp;End-to-End Testing (Playwright MCP)

1. Ensure both **backend** & **frontend** are running.
2. From the repo root:

   ```bash
   npx playwright test
   ```

3. Credentials for the login step (auto-filled by tests):

```
user:    <EMAIL>
pass:    Alvaro13.,
```

---

## 9&nbsp;&nbsp;Common Troubleshooting

| Symptom | Fix |
|---------|-----|
| `pg-to-ts` fails with _password authentication failed_ | Verify `DATABASE_URL` credentials. |
| Frontend shows CORS errors | Ensure backend `CORS` is enabled in `main.ts`. |
| Supabase realtime not working | Open ports 80/443 & 5432 on the instance; check Traefik rules. |

> Add new issues + solutions here as you encounter them.

---

## 10&nbsp;&nbsp;Useful Commands Reference

| Script | Location | Purpose |
|--------|----------|---------|
| `npm run lint` | `backend/` | ESLint + Prettier autofix |
| `npm run test` | `backend/` | Jest unit tests |
| `npm run build` | `backend/` | Production build to `dist/` |
| `npm run type-check` | `frontend/` | Frontend strict TS check |

---

Happy coding! 🚀