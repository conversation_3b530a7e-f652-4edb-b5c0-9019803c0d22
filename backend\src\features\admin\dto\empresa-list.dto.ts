import { ApiProperty } from '@nestjs/swagger';

export class EmpresaListItemDto {
  @ApiProperty({
    description: 'Unique identifier of the company',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Company name',
    example: 'Empresa Ejemplo S.L.',
  })
  nombre: string;

  @ApiProperty({
    description: 'Company logo URL',
    example: 'https://example.com/logo.png',
    required: false,
  })
  logo_url?: string;

  @ApiProperty({
    description: 'Company sector',
    example: 'Tecnología',
    required: false,
  })
  sector?: string;

  @ApiProperty({
    description: 'Number of active users in the company',
    example: 15,
  })
  total_usuarios: number;

  @ApiProperty({
    description: 'Number of active processes',
    example: 8,
  })
  total_procesos: number;
}

export class EmpresaListResponseDto {
  @ApiProperty({
    description: 'List of companies',
    type: [EmpresaListItemDto],
  })
  empresas: EmpresaListItemDto[];
}
