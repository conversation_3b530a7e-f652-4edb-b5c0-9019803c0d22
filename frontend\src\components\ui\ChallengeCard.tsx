import React from 'react';

type ChallengeStatus = 'pending' | 'completed' | 'expired';

interface ChallengeCardProps {
  title: string;
  description?: string;
  points: number;
  status: ChallengeStatus;
  onAction: () => void;
}

const statusStyles = {
  pending: {
    borderColor: 'border-blue-500',
    backgroundColor: 'bg-blue-50',
    textColor: 'text-blue-700',
    buttonText: 'Completar Reto',
    buttonColor: 'bg-blue-500 hover:bg-blue-600',
  },
  completed: {
    borderColor: 'border-green-500',
    backgroundColor: 'bg-green-50',
    textColor: 'text-green-700',
    buttonText: 'Completado',
    buttonColor: 'bg-green-500 cursor-not-allowed',
  },
  expired: {
    borderColor: 'border-gray-400',
    backgroundColor: 'bg-gray-100',
    textColor: 'text-gray-500',
    buttonText: 'Expirado',
    buttonColor: 'bg-gray-400 cursor-not-allowed',
  },
};

export const ChallengeCard: React.FC<ChallengeCardProps> = ({
  title,
  description,
  points,
  status,
  onAction,
}) => {
  const styles = statusStyles[status];

  return (
    <div
      className={`border-l-4 ${styles.borderColor} ${styles.backgroundColor} p-4 rounded-lg shadow-md flex justify-between items-center transition-all duration-300`}
    >
      <div className="flex-grow">
        <h3 className={`font-bold text-lg ${styles.textColor}`}>{title}</h3>
        {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
        <div className={`mt-2 font-semibold ${styles.textColor}`}>
          Recompensa: {points} puntos
        </div>
      </div>
      <div className="ml-4">
        <button
          onClick={onAction}
          disabled={status !== 'pending'}
          className={`px-4 py-2 text-white font-bold rounded-lg shadow-sm transition-colors duration-200 ${
            styles.buttonColor
          } ${status !== 'pending' ? 'opacity-70' : ''}`}
        >
          {styles.buttonText}
        </button>
      </div>
    </div>
  );
};