# n8n Workflow Error Handling

This document outlines the standard procedure for implementing robust error handling in all n8n workflows that interact with the application database. The goal is to ensure that no processing failure goes unnoticed and that we have a clear, actionable record of every error.

## Core Principle: The Dead-Letter Queue

We use a "Dead-Letter Queue" pattern, implemented via the `workflow_errores` table in the database. When a workflow encounters an unrecoverable error, its final action must be to insert a record into this table.

This provides three key benefits:
1.  **Persistence:** No error is lost. We have a permanent record of what failed and why.
2.  **Monitoring:** We can build automated alerts on this table to notify developers immediately of any issues.
3.  **Recovery:** It enables both manual and automated reprocessing of failed tasks.

## Standard Error Handling Implementation

Every complex workflow should have a dedicated error handling branch.

### 1. Setting Up the Error Trigger

In your n8n workflow, you can set the "Error Trigger" to be executed when a node fails. You can find this in the workflow settings. This trigger will start the error handling branch of your workflow.

### 2. The Error Handling Branch

The error handling branch should consist of at least two nodes:

**A. "Set Error Data" Node:**

This is a "Set" node that prepares the data to be inserted into the `workflow_errores` table. It should structure the data as follows:

*   **`workflow_name` (String):** The name of the workflow that failed. This should be a hardcoded string, e.g., "Procesador de Grabaciones".
*   **`payload` (JSON):** The input data that caused the workflow to fail. This is critical for debugging. You can usually get this from the input data of the failing node.
*   **`error_message` (String):** The specific error message. This can be retrieved from the `error` object provided by the n8n error trigger.

**B. "Insert to Database" Node:**

This is a "Postgres" node (or equivalent for our database) configured to perform an `INSERT` operation on the `workflow_errores` table.

*   **Table:** `public.workflow_errores`
*   **Columns:** `workflow_name`, `payload`, `error_message`
*   **Values:** Map the values from the "Set Error Data" node.

### 3. (Optional) Update Original Record Status

In addition to writing to the `workflow_errores` table, it is often necessary to update the status of the original record that was being processed. For example, in the "Procesador de Grabaciones" workflow, you should add another database node to:

*   **Table:** `grabaciones`
*   **Operation:** `UPDATE`
*   **Filter:** `id` = `{{ $json.payload.grabacionId }}`
*   **Columns to Update:**
    *   `estado_procesamiento`: Set to `'error'`
    *   `info_adicional`: Set to the error message.

This ensures that the frontend UI can immediately reflect that there was a problem with the specific item.

### Example Mermaid Diagram

```mermaid
graph TD
    A[Workflow Starts] --> B{Main Processing Node};
    B -- Success --> C[Continue Workflow];
    B -- Failure --> D[Error Trigger];
    D --> E[Set Error Data];
    E --> F[Insert into workflow_errores];
    E --> G[Update grabaciones status to 'error'];
```

By following this standard, we ensure that our asynchronous system is resilient, observable, and easier to debug.