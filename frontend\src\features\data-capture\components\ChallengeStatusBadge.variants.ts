import { cva } from 'class-variance-authority';

export const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-gray-500 text-primary-foreground hover:bg-gray-500/80',
        pending:
          'border-transparent bg-yellow-500 text-primary-foreground hover:bg-yellow-500/80',
        completed:
          'border-transparent bg-green-500 text-primary-foreground hover:bg-green-500/80',
        error:
          'border-transparent bg-red-500 text-destructive-foreground hover:bg-red-500/80',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);