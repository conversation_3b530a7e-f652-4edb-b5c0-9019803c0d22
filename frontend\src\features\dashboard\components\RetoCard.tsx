import { Link } from 'react-router-dom';
import { type RetosUsuarios } from '@aceleralia/types';

interface RetoCardProps {
  reto: RetosUsuarios;
}

export const RetoCard = ({ reto }: RetoCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow p-4 flex flex-col justify-between">
      <div>
        <h3 className="font-bold text-lg">{reto.titulo}</h3>
        <p className="text-sm text-gray-600 mt-1">{reto.descripcion}</p>
      </div>
      <div className="flex justify-between items-center mt-4">
        <span className="text-sm font-semibold text-green-600">
          {reto.puntos_recompensa} puntos
        </span>
        <Link
          to={reto.url_destino || '#'}
          className="bg-blue-500 text-white px-4 py-2 rounded-md text-sm font-semibold hover:bg-blue-600"
        >
          Comenzar
        </Link>
      </div>
    </div>
  );
};