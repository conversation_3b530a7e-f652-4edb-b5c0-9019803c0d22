import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGestorPanel } from '../hooks/useGestorPanel';
import { LoadingSpinner } from '../../../components/ui/LoadingSpinner';
import { Button } from '../../../components/ui/button';
import { SprintsTab } from './SprintsTab';
import { UsuariosTab } from './UsuariosTab';
import { RetosTab } from './RetosTab';

type TabType = 'sprints' | 'usuarios' | 'retos';

/**
 * Main management panel component for a selected company
 * Provides tabbed interface for sprints, users, and manual challenge creation
 */
export const GestorPanel = () => {
  const { empresaId } = useParams<{ empresaId: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>('sprints');
  
  const { data, isLoading, error } = useGestorPanel(empresaId!);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-600 text-lg font-medium mb-2">
            Error al cargar datos de la empresa
          </div>
          <div className="text-gray-600 mb-4">
            Por favor, intenta de nuevo más tarde
          </div>
          <Button onClick={() => navigate('/admin/gestor')}>
            Volver al Selector
          </Button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'sprints', label: 'Sprints y Progreso', icon: '📊' },
    { id: 'usuarios', label: 'Usuarios y Personas', icon: '👥' },
    { id: 'retos', label: 'Creación Manual de Retos', icon: '🎯' },
  ];

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header with Company Info */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {data.empresa.logo_url ? (
              <img
                src={data.empresa.logo_url}
                alt={`${data.empresa.nombre} logo`}
                className="w-16 h-16 rounded-lg object-cover mr-4"
              />
            ) : (
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <span className="text-blue-600 font-bold text-2xl">
                  {data.empresa.nombre.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {data.empresa.nombre}
              </h1>
              {data.empresa.sector && (
                <p className="text-gray-600">{data.empresa.sector}</p>
              )}
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate('/admin/gestor')}
          >
            Cambiar Empresa
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'sprints' && (
            <SprintsTab
              empresaId={empresaId!}
              sprintActivo={data.sprint_activo}
              historialSprints={data.historial_sprints}
            />
          )}

          {activeTab === 'usuarios' && (
            <UsuariosTab
              empresaId={empresaId!}
              usuariosPersonas={data.usuarios_personas}
              personasSinUsuario={data.personas_sin_usuario}
            />
          )}

          {activeTab === 'retos' && (
            <RetosTab
              usuariosPersonas={data.usuarios_personas}
            />
          )}
        </div>
      </div>

      {/* Quick Stats Footer */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Resumen Rápido</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">
              {data.usuarios_personas.length}
            </div>
            <div className="text-sm text-gray-600">Usuarios Activos</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">
              {data.usuarios_personas.reduce((sum, up) => sum + up.procesos_asignados, 0)}
            </div>
            <div className="text-sm text-gray-600">Procesos Asignados</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">
              {data.usuarios_personas.reduce((sum, up) => sum + up.retos_completados, 0)}
            </div>
            <div className="text-sm text-gray-600">Retos Completados</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">
              {data.usuarios_personas.reduce((sum, up) => sum + up.puntos_totales, 0)}
            </div>
            <div className="text-sm text-gray-600">Puntos Totales</div>
          </div>
        </div>
      </div>
    </div>
  );
};
