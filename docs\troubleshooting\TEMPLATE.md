# Troubleshooting Guide

This directory contains detailed troubleshooting guides for common issues encountered in the Aceleralia application.

## 📋 Available Guides

### Production Issues
- **[Production 404 & Mixed Content Error Fix](./PRODUCTION_404_MIXED_CONTENT_FIX.md)** - Critical fix for all backend endpoints returning 404 due to HTTPS/HTTP mismatch and FastAPI redirect issues

## 🔍 How to Use These Guides

Each troubleshooting guide follows this structure:
- **Problem Description** - Symptoms and error messages
- **Root Cause Analysis** - Technical explanation of the issue
- **Solution Implemented** - Step-by-step fix with code examples
- **Verification Steps** - How to test the fix
- **Prevention Measures** - How to avoid the issue in the future

## 🆘 Quick Reference

### Common Production Issues
1. **404 Errors on All Endpoints** → Check HTTPS configuration and route registration
2. **Mixed Content Errors** → Ensure both frontend and backend use HTTPS
3. **FastAPI 307 Redirects** → Implement dual route registration (with/without trailing slash)

### Debugging Tools
- **Health Check**: `GET /health`
- **Route Debug**: `GET /debug/routes`
- **Request Logs**: Check backend logs for detailed request information

### Emergency Contacts
- **Development Team**: Check Sprint logs in `docs/SPRINTS/`
- **Infrastructure**: Coolify deployment platform
- **Monitoring**: Backend health check endpoints

## 📚 Related Documentation
- [Setup Guide](../SETUP_GUIDE.md) - Deployment and configuration
- [Architecture](../ARCHITECTURE.md) - System overview
- [Sprint Logs](../SPRINTS/) - Development history and decisions
