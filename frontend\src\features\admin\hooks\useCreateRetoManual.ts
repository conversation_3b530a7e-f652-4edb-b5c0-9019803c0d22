import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../../../lib/api';
import { useToastStore } from '../../../store/toast';

interface CreateRetoManualRequest {
  titulo: string;
  descripcion?: string;
  tipo_accion: 'definir_proceso' | 'responder_pregunta' | 'definir_tarea' | 'definir_duracion_proceso';
  puntos_recompensa: number;
  usuario_ids: string[];
  entidad_relacionada_id?: string;
  entidad_relacionada_tipo?: 'proceso_cliente' | 'pregunta_cliente' | 'tarea_cliente';
}

interface CreateRetoManualResponse {
  retos_creados: number;
  reto_ids: string[];
  message: string;
}

/**
 * Hook to create manual challenges
 */
export const useCreateRetoManual = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: async (data: CreateRetoManualRequest): Promise<CreateRetoManualResponse> => {
      const response = await api.post<CreateRetoManualResponse>('/admin/retos/manual', data);
      return response.data;
    },
    onSuccess: (data) => {
      showToast(`${data.retos_creados} retos creados y asignados exitosamente`, 'success');
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['admin', 'gestor-panel'] });
    },
    onError: (error: any) => {
      showToast(error.response?.data?.message || 'Error al crear retos', 'error');
    },
  });
};
