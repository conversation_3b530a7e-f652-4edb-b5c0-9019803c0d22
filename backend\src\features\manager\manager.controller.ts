import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ManagerService } from './manager.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ManagerRoleGuard } from '../auth/guards/manager-role.guard';
import { User } from '../../common/decorators/user.decorator';
import { Usuarios } from '../../common/database.types';
import { DashboardSummaryDto } from './dto/dashboard-summary.dto';
import {
  ProcessListQueryDto,
  ProcessListResponseDto,
} from './dto/process-list.dto';
import {
  FindingListQueryDto,
  FindingListResponseDto,
} from './dto/finding-list.dto';
import { ProcessDetailDto } from './dto/process-detail.dto';
import { FindingDetailDto } from './dto/finding-detail.dto';

/**
 * Controller for manager dashboard and analytics
 * Protected by JWT authentication and manager role guard
 */
@ApiTags('Manager Dashboard')
@ApiBearerAuth()
@Controller('manager')
@UseGuards(JwtAuthGuard, ManagerRoleGuard)
export class ManagerController {
  constructor(private readonly managerService: ManagerService) {}

  @Get('dashboard-summary')
  @ApiOperation({
    summary: 'Get manager dashboard summary',
    description:
      'Returns comprehensive dashboard data including KPIs, charts, and recent activity',
  })
  @ApiResponse({
    status: 200,
    description: 'Dashboard summary retrieved successfully',
    type: DashboardSummaryDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Manager role required.',
  })
  getDashboardSummary(@User() user: Usuarios): Promise<DashboardSummaryDto> {
    return this.managerService.getDashboardSummary(user);
  }

  @Get('procesos')
  @ApiOperation({
    summary: 'Get paginated list of processes',
    description: 'Returns filtered and paginated list of company processes',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({
    name: 'departamento',
    required: false,
    description: 'Filter by department',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in process name',
  })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order (asc/desc)',
  })
  @ApiResponse({
    status: 200,
    description: 'Processes retrieved successfully',
    type: ProcessListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Manager role required.',
  })
  getProcesses(
    @User() user: Usuarios,
    @Query() query: ProcessListQueryDto,
  ): Promise<ProcessListResponseDto> {
    return this.managerService.getProcesses(user, query);
  }

  @Get('procesos/:id')
  @ApiOperation({
    summary: 'Get detailed process information',
    description:
      'Returns comprehensive details for a specific process including tasks, responsibles, and information sources',
  })
  @ApiParam({ name: 'id', description: 'Process ID' })
  @ApiResponse({
    status: 200,
    description: 'Process details retrieved successfully',
    type: ProcessDetailDto,
  })
  @ApiResponse({ status: 404, description: 'Process not found' })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Manager role required.',
  })
  getProcessDetail(
    @User() user: Usuarios,
    @Param('id') processId: string,
  ): Promise<ProcessDetailDto> {
    return this.managerService.getProcessDetail(user, processId);
  }

  @Get('hallazgos-clientes')
  @ApiOperation({
    summary: 'Get paginated list of findings',
    description: 'Returns filtered and paginated list of identified findings',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({
    name: 'tipo',
    required: false,
    description: 'Filter by finding type',
  })
  @ApiQuery({
    name: 'departamento',
    required: false,
    description: 'Filter by department',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in title or description',
  })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order (asc/desc)',
  })
  @ApiResponse({
    status: 200,
    description: 'Findings retrieved successfully',
    type: FindingListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Manager role required.',
  })
  getFindings(
    @User() user: Usuarios,
    @Query() query: FindingListQueryDto,
  ): Promise<FindingListResponseDto> {
    return this.managerService.getFindings(user, query);
  }

  @Get('hallazgos-clientes/:id')
  @ApiOperation({
    summary: 'Get detailed finding information',
    description: 'Returns comprehensive details for a specific finding',
  })
  @ApiParam({ name: 'id', description: 'Finding ID' })
  @ApiResponse({
    status: 200,
    description: 'Finding details retrieved successfully',
    type: FindingDetailDto,
  })
  @ApiResponse({ status: 404, description: 'Finding not found' })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Manager role required.',
  })
  getFindingDetail(
    @User() user: Usuarios,
    @Param('id') findingId: string,
  ): Promise<FindingDetailDto> {
    return this.managerService.getFindingDetail(user, findingId);
  }
}
