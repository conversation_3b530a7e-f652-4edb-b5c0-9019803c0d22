import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Usuarios } from '../../../common/database.types';

/**
 * Guard that ensures only users with 'cliente_gerente' role can access protected routes
 */
@Injectable()
export class ManagerRoleGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const request = context.switchToHttp().getRequest();
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    const user: Usuarios = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (user.rol !== 'cliente_gerente') {
      throw new ForbiddenException('Access denied. Manager role required.');
    }

    return true;
  }
}
