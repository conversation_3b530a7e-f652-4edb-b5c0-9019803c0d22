import { Module } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { DatabaseModule } from '../../common/database.module';
import { RetosModule } from '../retos/retos.module';

@Module({
  imports: [DatabaseModule, RetosModule],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}
