import { create } from 'zustand';
import type { User } from '@supabase/supabase-js';

interface SessionState {
  token: string | null;
  user: User | null;
  setToken: (token: string | null) => void;
  setUser: (user: User | null) => void;
}

export const useSessionStore = create<SessionState>((set) => ({
  token: null,
  user: null,
  setToken: (token) => set({ token }),
  setUser: (user) => set({ user }),
}));