import { useMutation, useQueryClient } from '@tanstack/react-query';
import { definirDuracion } from '@/lib/api';
import { useToastStore } from '@/store/toast';

export const useDefinirDuracion = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: (data: {
      proceso_id: string;
      duracion: number;
      periodo: string;
    }) => definirDuracion(data),
    onSuccess: () => {
      showToast('Duración definida correctamente', 'success');
      queryClient.invalidateQueries({ queryKey: ['procesos-clientes'] });
    },
    onError: (error) => {
      showToast(`Error al definir la duración: ${error.message}`, 'error');
    },
  });
};