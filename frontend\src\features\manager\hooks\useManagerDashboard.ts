import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { type ManagerDashboardSummary } from '@aceleralia/types';

const getManagerDashboardSummary = async (): Promise<ManagerDashboardSummary> => {
  const response = await apiClient.get('/manager/dashboard-summary');
  return response.data;
};

export const useManagerDashboard = () => {
  return useQuery({
    queryKey: ['managerDashboard'],
    queryFn: getManagerDashboardSummary,
  });
};
