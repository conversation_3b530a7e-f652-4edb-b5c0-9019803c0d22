# n8n Workflow: Gamification and Notifications

This document outlines the n8n workflow responsible for awarding points and sending notifications to users after their submissions are processed.

## 1. Workflow Trigger

- **Type:** Webhook
- **Method:** `POST`
- **URL:** The URL provided by the n8n webhook node.
- **Authentication:** The webhook should be configured to require an API key sent in the `x-api-key` header. The value of this key must match the `N8N_API_KEY` environment variable in the backend.

## 2. Input Data

The webhook expects a JSON body with the following structure:

```json
{
  "userId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "action": "definir_proceso",
  "subtaskId": "a1b2c3d4-e5f6-7890-1234-567890abcdef"
}
```

- `userId`: The ID of the user who completed the action.
- `action`: The type of action completed (e.g., `definir_proceso`, `responder_pregunta`).
- `subtaskId`: The ID of the subtask that was completed.

## 3. Workflow Steps

1.  **Webhook Trigger:**
    - Receives the initial data from the backend.

2.  **Call Backend to Award Points:**
    - **Node Type:** HTTP Request
    - **Method:** `POST`
    - **URL:** `http://<your-backend-host>/system/award-points`
    - **Authentication:** Header Auth.
        - **Name:** `x-api-key`
        - **Value:** Your `N8N_API_KEY`.
    - **Body:** The JSON data received by the webhook.

3.  **Create Notification in Database:**
    - **Node Type:** Supabase
    - **Operation:** Insert
    - **Table:** `notificaciones`
    - **Columns:**
        - `usuario_id`: `{{ $json.body.userId }}`
        - `titulo`: "¡Reto Completado!"
        - `mensaje`: `Has ganado 10 puntos por completar la tarea.`
        - `tipo_notificacion`: `reto_completado`

4.  **Update `grabaciones` record:**
    - **Node Type:** Supabase
    - **Operation:** Update
    - **Table:** `grabaciones`
    - **Match Column:** `entidad_relacionada_id` (This assumes the subtask ID is also the recording ID. You may need to adjust this based on your data model).
    - **Value:** `{{ $json.body.subtaskId }}`
    - **Columns to Update:**
        - `estado_procesamiento`: `completado`

## 4. Expected Outcome

- The user receives points for their contribution.
- A notification is created for the user in the database.
- The user sees a real-time notification in the application.
- The `grabaciones` record is marked as `completado`.