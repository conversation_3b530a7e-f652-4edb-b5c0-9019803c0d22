import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import axios from 'axios';
import n8nConfig, { N8nWorkflow } from 'src/config/n8n.config';

export interface NewRecordingPayload {
  version: '1.0';
  eventType: 'new_recording';
  data: {
    grabacionId: string;
    usuarioId: string;
    entidadRelacionadaId: string;
    entidadRelacionadaTipo:
      | 'proceso_cliente'
      | 'pregunta_cliente'
      | 'tarea_cliente';
  };
}

type WebhookPayload = NewRecordingPayload; // Add other payload types with |

@Injectable()
export class N8NWebhookService {
  private readonly logger = new Logger(N8NWebhookService.name);

  constructor(
    @Inject(n8nConfig.KEY)
    private readonly n8nConfiguration: ConfigType<typeof n8nConfig>,
  ) {}

  private getWebhookUrl(workflow: N8nWorkflow): string {
    const url = this.n8nConfiguration.workflows[workflow]?.url;
    if (!url) {
      throw new Error(
        `Webhook URL for workflow '${workflow}' is not configured.`,
      );
    }
    return url;
  }

  async triggerWorkflow(
    workflow: N8nWorkflow,
    payload: WebhookPayload,
  ): Promise<void> {
    const webhookUrl = this.getWebhookUrl(workflow);

    try {
      this.logger.log(`Triggering n8n workflow '${workflow}' at ${webhookUrl}`);
      await axios.post(webhookUrl, payload, {
        headers: { 'Content-Type': 'application/json' },
      });
      this.logger.log(`Successfully triggered n8n workflow '${workflow}'`);
    } catch (error) {
      const stack = error instanceof Error ? error.stack : String(error);
      this.logger.error(`Failed to trigger n8n workflow '${workflow}'`, stack);
      // Do not rethrow to avoid blocking the main application flow
    }
  }
}
