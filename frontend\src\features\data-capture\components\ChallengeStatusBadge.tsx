import React from 'react';
import { type VariantProps } from 'class-variance-authority';
import { badgeVariants } from './ChallengeStatusBadge.variants';

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  status: 'pendiente' | 'completado' | 'error' | 'en_procesamiento';
  errorMessage?: string | null;
}

function ChallengeStatusBadge({ className, status, errorMessage, ...props }: BadgeProps) {
  const getVariant = () => {
    switch (status) {
      case 'pendiente':
        return 'pending';
      case 'en_procesamiento':
        return 'pending';
      case 'completado':
        return 'completed';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = () => {
    switch (status) {
        case 'pendiente':
            return 'Pendiente';
        case 'en_procesamiento':
            return 'Procesando';
        case 'completado':
            return 'Completado';
        case 'error':
            return 'Error';
        default:
            return 'Desconocido';
    }
  }

  return (
    <div
      className={badgeVariants({ variant: getVariant(), className })}
      title={status === 'error' ? errorMessage || 'Se ha producido un error' : ''}
      {...props}
    >
      {getStatusText()}
    </div>
  );
}

export { ChallengeStatusBadge };