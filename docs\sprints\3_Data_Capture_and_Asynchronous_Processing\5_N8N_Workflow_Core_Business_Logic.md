# Sprint 18: N8N Workflow - Core Business Logic

## 1. Sprint Goal
The goal of this sprint is to extend the n8n workflow to perform the core business logic of the application: sending the media file to an AI service for analysis and then using that output to update the relevant business tables in the database.

## 2. Key Tasks
- **Refactor N8N Webhook Service:**
    - Create a centralized configuration for n8n webhooks in `backend/src/config/n8n.config.ts`.
    - Refactor `N8NWebhookService` to use the centralized configuration.
    - Integrate the refactored service into `GrabacionesService`.
- **Define n8n Workflow:**
    - Create a new documentation file `docs/n8n/workflows.md` to define the structure and logic of the "Process Recording" workflow.
- **Update Documentation:**
    - Update `docs/ARCHITECTURE.md` to reflect the new file structure and refactored n8n integration.
    - Update `docs/modulos/3_Data_Capture_and_Asynchronous_Processing.md` to reference the new n8n documentation.

## 3. Acceptance Criteria
- The `N8NWebhookService` is refactored to use a centralized configuration.
- The "Process Recording" workflow is clearly defined in the documentation.
- All relevant documentation is updated to reflect the changes.

## 4. Key Files to Be Created/Modified
- **Code:**
    - `backend/src/config/n8n.config.ts` (created)
    - `backend/src/shared/n8n-webhook.service.ts` (modified)
    - `backend/src/features/grabaciones/grabaciones.service.ts` (modified)
    - `backend/src/app.module.ts` (modified)
- **Documentation:**
    - `docs/n8n/workflows.md` (created)
    - `docs/ARCHITECTURE.md` (modified)
    - `docs/modulos/3_Data_Capture_and_Asynchronous_Processing.md` (modified)