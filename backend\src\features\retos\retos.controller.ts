import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { RetosService } from './retos.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../../common/decorators/user.decorator';
import { Usuarios } from '../../common/database.types';

@Controller('retos')
@UseGuards(JwtAuthGuard)
export class RetosController {
  constructor(private readonly retosService: RetosService) {}

  @Get()
  findAll(@User() user: Usuarios, @Query('estado') estado: string) {
    return this.retosService.findAll(user.id, estado);
  }
}
