import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import { Usuarios } from '../database.types';

interface AuthenticatedRequest extends Request {
  user: Usuarios;
}

export const User = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): Usuarios => {
    const request = ctx.switchToHttp().getRequest<AuthenticatedRequest>();
    return request.user;
  },
);
