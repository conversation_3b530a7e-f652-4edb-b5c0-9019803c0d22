# Feature: 4. Manager's Dashboard & Analytics

## 1. Overview

This module provides the strategic value proposition for the client's management. It transforms the raw data collected from employees into a high-level, interactive, and actionable intelligence dashboard. It allows managers to get a 360º view of their company's processes, identify inefficiencies, and track the progress of the consultancy engagement in real-time.

## 2. Requirements

*   **Requirement 1 (Role-Based Access):** Only users with the `cliente_gerente` role can access this module.
*   **Requirement 2 (At-a-Glance Dashboard):** The main dashboard must provide a quick, visual summary of key business metrics, including KPIs and charts.
*   **Requirement 3 (Data Exploration):** Managers must be able to drill down from the high-level dashboard into detailed, filterable views of processes and findings.
*   **Requirement 4 (Integrated Experience):** The manager's view must seamlessly integrate their own personal tasks and challenges, recognizing their dual role as both a leader and a participant.
*   **Requirement 5 (Detailed Views):** The detail views for a single process or finding must be comprehensive, showing all related information, including AI-generated descriptions and associated people/departments.

## 3. UI/UX Design

*   **Key Screens/Components:**
    *   **Manager's Dashboard (`/portal/dashboard`):** A grid of data visualizations including `<KPI_Card>` components (e.g., "Procesos Identificados"), charts (e.g., "Hallazgos por Tipo"), a compact `<RankingList>`, and the manager's own `<RetoCard>`.
    *   **Processes Explorer (`/portal/procesos`):** A full-page view featuring a powerful `<DataGrid>` component for filtering and sorting all company processes.
    *   **Findings Explorer (`/portal/hallazgos`):** A similar full-page view with a `<DataGrid>` for exploring all identified findings.
    *   **Detail Pane (`<Modal>` or side panel):** A component that displays the full, detailed information for a selected process or finding, triggered by clicking a row in the explorer views.
*   **User Flow (Data Exploration):**
    1.  Manager logs in and lands on the main dashboard.
    2.  They see a chart showing "Procesos por Departamento" and notice the "Sales" department has the most.
    3.  They navigate to the "Procesos" explorer page.
    4.  They filter the `<DataGrid>` by "Departamento: Sales".
    5.  They click on a specific process, "Gestión de Leads Entrantes", to open its detail pane.
    6.  In the detail pane, they review the full AI-generated SOP, associated tasks, and responsible individuals.

## 4. Technical Details

*   **Frontend:**
    *   **Relevant Components:** `<KPI_Card>`, `<DataGrid>`, `<Modal>`, Charting library (e.g., Recharts, Chart.js).
    *   **State Management:** TanStack Query is essential here for managing the fetching, caching, and filtering of the large datasets required for the explorers. URL state can be used to persist filter settings.
    *   **API Interactions:**
        *   `GET /manager/dashboard-summary`: An aggregate endpoint to efficiently load all data for the main dashboard in a single call.
        *   `GET /procesos`: A paginated and filterable endpoint for the processes explorer.
        *   `GET /procesos/{id}`: Fetches the detailed, "hydrated" data for a single process.
        *   `GET /hallazgos-clientes`: A paginated and filterable endpoint for the findings explorer.
        *   `GET /hallazgos-clientes/{id}`: Fetches the detailed data for a single finding.
*   **Backend:**
    *   **Relevant API Routes:** As listed above. All routes within a `/manager` group will be protected by a guard that checks for the `cliente_gerente` role.
    *   **Services Used:** `DashboardService`, `DatabaseService`. The `DashboardService` will contain complex SQL queries (or views) to aggregate data efficiently.
    *   **Database Tables Involved:** Primarily read-heavy operations on `procesos_clientes`, `hallazgos_clientes`, `usuarios`, `departamentos`, and their respective join tables.
    *   **External Services:** None. This module is focused on presenting data already in our system.

## 5. Acceptance Criteria

*   Given a user with the `cliente_empleado` role, when they attempt to access `/portal/dashboard`, then they should be denied access.
*   Given a manager is on the dashboard, when data is updated (e.g., a new process is defined), then the KPIs and charts on the dashboard should reflect the new data upon refresh.
*   Given a manager is on the processes explorer, when they apply a filter, then the `<DataGrid>` should update to show only the matching results.
*   Given a manager clicks on a process, when the detail pane opens, then it must display the full `descripcion_detallada` and a list of all related tasks and responsible people.

## 6. Notes & Decisions

*   Performance is critical for this module. The backend endpoints, especially `GET /manager/dashboard-summary`, must be highly optimized. This may involve creating database views or functions to pre-aggregate data and reduce the complexity of API-side queries.
*   The `<DataGrid>` component will be a significant piece of frontend development. It should be built as a generic, reusable component that can be configured with different columns and filter types.
*   The choice of charting library should be made carefully, prioritizing ease of use, performance, and visual appeal.