# Sprint 17 (Revised): Asynchronous Workflow Integration & Contracts

## 1. Sprint Goal
The goal of this sprint is to implement the application's side of the asynchronous processing integration. This involves defining the precise "contract" for the n8n workflow, triggering it correctly, and specifying the expected outcomes that the application will look for in the database once the workflow completes.

## 2. Key Tasks

### 2.a. Define the Workflow Trigger & Payload
- **Service:** `N8N_WebhookService`
- **Action:** When a `grabaciones` record is created, the service must trigger the n8n webhook.
- **Webhook URL Management:** The specific URL for the n8n webhook will not be hardcoded. It will be loaded from the backend's environment variables (`.env` file). Example: `N8N_PROCESS_RECORDING_WEBHOOK_URL=...`
- **Payload Definition:** The code will be responsible for sending a clear, versioned JSON payload.
  ```json
  {
    "version": "1.0",
    "eventType": "new_recording",
    "data": {
      "grabacionId": "uuid-of-the-recording",
      "usuarioId": "uuid-of-the-user",
      "entidadRelacionadaId": "uuid-of-the-process-or-question",
      "entidadRelacionadaTipo": "proceso_cliente"
    }
  }
  ```

### 2.b. Define the Expected Asynchronous Outcome
- The application code does not wait for a response. It is a "fire-and-forget" trigger.
- The application will later confirm success by observing changes in the database state. The code must be aware of the following expected changes, which will be performed by the n8n workflow:
    - **In `grabaciones` table:** `estado_procesamiento` changes from `pendiente` -> `en_procesamiento` -> `completado` or `error`.
    - **In `procesos_clientes` table:** `descripcion_detallada` is populated.
    - **In `retos_subtareas` table:** `estado` changes to `completado`.
    - **In `usuarios` table:** `puntos` are increased.
    - **In `notificaciones` table:** A new record is created for the user.

### 2.c. Development Coordination
- **Action Item:** Before starting development, the developer must ask the user (who manages n8n): "Do you want to develop the n8n workflow simultaneously so we can perform live integration tests, or should I proceed with the application code first and mock the expected database changes for testing?"
- **Decision:** Proceed with application code first.

## 3. Acceptance Criteria
- The `N8N_WebhookService` correctly calls the n8n webhook URL with the precisely defined JSON payload upon a new recording.
- The application's logic for other features (e.g., displaying user points, notifications) correctly reflects the database changes that are *expected* to be made by the n8n workflow.
- The development team has a clear, documented "contract" of the payload and expected outcomes to share with the n8n workflow developer.

## 4. Key Files to Be Created/Modified
- `backend/src/shared/n8n-webhook.service.ts` (Modified to use the new payload)
- `backend/src/grabaciones/grabaciones.service.ts` (Modified to trigger the webhook)
- `docs/ARCHITECTURE.md` (Modified to document the webhook contract)

## 5. Session Log

### Technical Implementation Summary
- Created a new `N8NWebhookService` responsible for handling all outgoing webhooks to n8n.
- The service reads the specific webhook URL from environment variables (`N8N_PROCESS_RECORDING_WEBHOOK_URL`) to avoid hardcoding secrets.
- Implemented a `NewRecordingPayload` interface to enforce the structure of the JSON payload, ensuring a strict contract.
- Modified the `GrabacionesService` to use the new `N8NWebhookService`. When a new recording is created, it now constructs the full payload and triggers the `processRecording` workflow.
- The trigger is a "fire-and-forget" operation with error logging that does not block the main application thread, ensuring a responsive user experience.
- Updated the `SharedModule` to correctly provide the `ConfigModule` to the `N8NWebhookService`.
- Documented the complete webhook contract, including the payload and expected database state changes, in `docs/ARCHITECTURE.md`.

### Conclusion
The application-side implementation for the n8n workflow integration is complete. The code is now ready for integration testing once the n8n workflow is available. All acceptance criteria for this sprint have been met.