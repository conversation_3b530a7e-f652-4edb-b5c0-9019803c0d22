 El Panel de Control de Aceleralia (Visión Definitiva y Enfocada)
Propósito General: Este módulo es el centro de mando unificado de Aceleralia. Su diseño se basa en un principio fundamental: "Seleccionar para Actuar". El administrador primero elige una empresa cliente y, a continuación, se le presenta un panel de control completo y contextualizado con todas las herramientas necesarias para gestionar, supervisar e intervenir en el progreso de esa empresa específica.

1. Frontend (UI/UX)
A) La Estructura General: Una Interfaz de Dos Niveles

La interfaz se diseñará para evitar la sobrecarga de información, guiando al administrador de forma natural desde una vista global a una específica.

Nivel 1: La Pantalla de Selección de Empresa (/admin/gestor)

Diseño: Al entrar en la sección de administración, la primera y única cosa que verá el usuario será una pantalla limpia y centrada con el título "Gestor de Clientes" y un único componente principal: un buscador/selector de empresas.
Interacción: Este no será un simple dropdown. Será un campo de búsqueda inteligente que, al empezar a escribir, filtrará en tiempo real la lista de empresas cliente. Cada resultado mostrará el logo y el nombre de la empresa. Al seleccionar una, la aplicación navegará a la vista de detalle.
Nivel 2: El Panel de Gestión por Empresa (/admin/gestor/{empresaId})

Diseño: Una vez seleccionada una empresa, se carga el panel de control principal. Este panel tendrá una navegación lateral secundaria (sub-sidebar) con las diferentes herramientas de gestión para esa empresa. El nombre de la empresa seleccionada aparecerá de forma prominente en la cabecera para mantener siempre el contexto.
Sub-Navegación: Las pestañas de esta barra lateral serán:
Sprints y Progreso
Usuarios y Personas
Creación Manual de Retos
B) Detalle de las Pestañas del Panel de Gestión

1. Pestaña: "Sprints y Progreso"

Propósito: Supervisar y gestionar los marcos de tiempo del proyecto.
Componentes:
Botón "Crear Nuevo Sprint": Abre un modal con un formulario para definir título, fecha_inicio y fecha_fin.
Gráfico de Progreso del Sprint Activo: Una gran barra de progreso visual que muestra los días transcurridos vs. los totales. Debajo, un gráfico de Donut mostrará "Retos Completados vs. Pendientes" para ese sprint, dándote una visión instantánea del avance real.
Tabla "Historial de Sprints": Una tabla de datos (<DataGrid />) que lista todos los sprints pasados de esa empresa, con su duración y el porcentaje de retos que se completaron en ese periodo.
2. Pestaña: "Usuarios y Personas"

Propósito: Gestionar el personal del cliente y analizar su rendimiento individual.
Componentes:
Botón "Crear Nuevo Usuario": Esta es una funcionalidad crítica y tendrá un flujo en dos pasos para garantizar la integridad de los datos:
Paso 1 (Modal): Un formulario para crear la cuenta de usuario. Campos: Email, Contraseña Inicial, Rol (dropdown con cliente_gerente y cliente_empleado). Al pulsar "Crear", se llama al backend.
Paso 2 (Modal, tras éxito): La vista del modal cambia y muestra: "Usuario creado con éxito. Ahora, vincúlalo a una persona existente". Debajo, un buscador/selector de personas de esa empresa que aún no tengan un usuario_id asignado. Al seleccionar una persona y pulsar "Vincular", se completa el proceso.
Tabla de Usuarios y Personas: Una tabla de datos avanzada que lista a todos los empleados de la empresa. Columnas: Nombre, Cargo, Puntos Totales, Retos Completados, Nº Retos Diarios (este último será un campo editable directamente en la tabla para que puedas ajustar la intensidad sobre la marcha).
Vista de Detalle por Persona (Drill-down): Al hacer clic en una fila de la tabla, se abrirá un panel lateral o una vista detallada que mostrará todo lo que has pedido:
KPIs de la Persona: Tarjetas con: Procesos Asignados, Hallazgos Reportados, Retos Completados, Puntos Acumulados.
Tabla "Procesos de [Nombre]": Una lista de todos los procesos de los que es responsable.
Tabla "Hallazgos de [Nombre]": Una lista de todos los hallazgos asociados a esa persona.
3. Pestaña: "Creación Manual de Retos"

Propósito: Darte el poder de crear y asignar retos de forma manual, ya sea a individuos o en masa.
Componentes:
Selector de Tipo de Reto: Botones para elegir qué tipo de reto crear (Definir Proceso, Responder Pregunta, etc.).
Formulario Dinámico: El formulario cambiará según el tipo de reto seleccionado. Por ejemplo, si eliges "Responder Pregunta", aparecerá un campo para escribir el título de la pregunta.
Panel de Asignación: Un componente potente para seleccionar los destinatarios:
Un buscador/selector múltiple de usuarios de la empresa.
Un botón "Seleccionar Todos" para asignar el reto a todos los empleados de la empresa con un solo clic.
Botón "Crear y Asignar Reto(s)": Inicia el proceso en el backend.
2. Backend (Lógica Conceptual - NestJS)
Endpoint (GET /admin/empresas): Devuelve la lista de todas las empresas para el selector inicial.
Endpoint (GET /admin/gestor/{empresaId}): Un endpoint de agregación que devuelve todos los datos necesarios para el panel de una empresa (sus sprints, usuarios, etc.).
Endpoint (POST /admin/usuarios): Gestiona la creación del registro en auth.users y en nuestra tabla usuarios.
Endpoint (PATCH /admin/personas/{personaId}/vincular): Recibe un usuarioId y lo asigna a la persona.
Endpoint (GET /admin/personas/{personaId}/detalle): Devuelve todos los datos agregados para la vista de detalle de una persona.
Endpoint (POST /admin/retos/manual): Un endpoint potente que recibe los detalles de un reto y una lista de usuarioIds. Itera sobre la lista y crea una entrada en retos_usuarios (y retos_subtareas si es necesario) para cada uno. Es una operación de creación en masa (bulk insert).



## Arquitectura de la Interfaz

### Nivel 1: Pantalla de Selección de Empresa (`/admin/gestor`)

**Propósito**: Punto de entrada limpio y centrado para seleccionar la empresa a gestionar.

**Características**:
- Título prominente "Gestor de Clientes"
- Buscador inteligente con filtrado en tiempo real
- Visualización de empresas con logo, nombre, sector y estadísticas básicas
- Navegación directa al panel de gestión al seleccionar una empresa

**Componentes**:
- `EmpresaSelector.tsx`: Componente principal de selección
- `useEmpresas.ts`: Hook para obtener lista de empresas

### Nivel 2: Panel de Gestión por Empresa (`/admin/gestor/{empresaId}`)

**Propósito**: Panel de control completo para gestionar una empresa específica.

**Estructura**:
- Cabecera con información de la empresa seleccionada
- Navegación lateral secundaria con tres pestañas principales
- Área de contenido contextual según la pestaña activa
- Resumen rápido con KPIs agregados

## Pestañas del Panel de Gestión

### 1. Sprints y Progreso

**Funcionalidades**:
- **Crear Nuevo Sprint**: Modal con formulario para título, fecha_inicio y fecha_fin
- **Progreso del Sprint Activo**: 
  - Barra de progreso visual (días transcurridos vs. totales)
  - Gráfico de donut mostrando "Retos Completados vs. Pendientes"
- **Historial de Sprints**: Tabla DataGrid con todos los sprints pasados y sus métricas

**Componentes**:
- `SprintsTab.tsx`: Componente principal de la pestaña
- `useCreateSprint.ts`: Hook para crear sprints

### 2. Usuarios y Personas

**Funcionalidades**:
- **Crear Nuevo Usuario**: Flujo en dos pasos
  - Paso 1: Crear cuenta de usuario (email, contraseña, rol)
  - Paso 2: Vincular a persona existente sin usuario
- **Tabla de Usuarios y Personas**: DataGrid avanzada con:
  - Información completa de cada empleado
  - Campo editable para número de retos diarios
  - Acción "Ver Detalle" para drill-down
- **Vista de Detalle por Persona**: Panel completo con:
  - KPIs: Procesos Asignados, Hallazgos Reportados, Retos Completados, Puntos
  - Tabla de procesos responsables
  - Tabla de hallazgos reportados

**Componentes**:
- `UsuariosTab.tsx`: Componente principal de la pestaña
- `useCreateUsuario.ts`: Hook para crear y vincular usuarios
- `usePersonaDetalle.ts`: Hook para obtener información detallada

### 3. Creación Manual de Retos

**Funcionalidades**:
- **Selector de Tipo de Reto**: Botones para diferentes tipos
  - Definir Proceso
  - Responder Pregunta
  - Definir Tarea
  - Definir Duración de Proceso
- **Formulario Dinámico**: Cambia según el tipo de reto seleccionado
- **Panel de Asignación Potente**:
  - Selector múltiple de usuarios
  - Botón "Seleccionar Todos"
  - Vista previa del reto antes de crear

**Componentes**:
- `RetosTab.tsx`: Componente principal de la pestaña
- `useCreateRetoManual.ts`: Hook para crear retos manuales

## Backend - Endpoints API

### Seguridad
- **Guard**: `AdminRoleGuard` - Verifica rol `admin_aceleralia`
- **Autenticación**: JWT requerido en todos los endpoints
- **Autorización**: Solo usuarios admin_aceleralia pueden acceder

### Endpoints Principales

#### 1. Gestión de Empresas
```typescript
GET /admin/empresas
// Retorna lista de todas las empresas cliente con estadísticas básicas
```

#### 2. Panel de Gestión
```typescript
GET /admin/gestor/{empresaId}
// Retorna datos completos del panel: empresa, sprints, usuarios, personas
```

#### 3. Gestión de Usuarios
```typescript
POST /admin/usuarios
// Crea nuevo usuario en Supabase Auth y tabla usuarios

PATCH /admin/personas/{personaId}/vincular
// Vincula usuario existente a una persona
```

#### 4. Gestión de Sprints
```typescript
POST /admin/sprints
// Crea nuevo sprint para una empresa
```

#### 5. Creación de Retos
```typescript
POST /admin/retos/manual
// Crea retos manuales para múltiples usuarios (bulk operation)
```

#### 6. Información Detallada
```typescript
GET /admin/personas/{personaId}/detalle
// Retorna información completa de una persona con KPIs y datos relacionados
```

## Características Técnicas

### Frontend
- **Framework**: React con TypeScript
- **Estado**: React Query para cache y sincronización
- **UI**: Tailwind CSS con componentes reutilizables
- **Navegación**: React Router con rutas protegidas
- **Notificaciones**: Sistema de toast integrado

### Backend
- **Framework**: NestJS con TypeScript
- **Base de Datos**: Supabase PostgreSQL
- **Autenticación**: Supabase Auth + JWT
- **Documentación**: Swagger/OpenAPI
- **Validación**: class-validator para DTOs

### Seguridad
- **Autenticación**: JWT obligatorio
- **Autorización**: Guard específico para admin_aceleralia
- **Validación**: Entrada validada en todos los endpoints
- **Manejo de Errores**: Respuestas consistentes y seguras

## Flujos de Trabajo Principales

### 1. Acceso al Panel
1. Usuario admin_aceleralia hace login
2. Navega a `/admin/gestor`
3. Selecciona empresa de la lista
4. Accede al panel completo de la empresa

### 2. Creación de Usuario
1. Admin hace clic en "Crear Nuevo Usuario"
2. Completa formulario de usuario (Step 1)
3. Sistema crea cuenta en Supabase Auth
4. Admin selecciona persona para vincular (Step 2)
5. Sistema actualiza persona con usuario_id

### 3. Creación de Retos Manuales
1. Admin selecciona tipo de reto
2. Completa formulario dinámico
3. Selecciona usuarios destinatarios
4. Revisa vista previa
5. Sistema crea retos para todos los usuarios seleccionados

## Métricas y KPIs

### Panel Principal
- Total de usuarios activos
- Procesos asignados (suma)
- Retos completados (suma)
- Puntos totales acumulados

### Por Sprint
- Días transcurridos vs. totales
- Retos completados vs. pendientes
- Porcentaje de completitud

### Por Persona
- Procesos asignados
- Hallazgos reportados
- Retos completados
- Puntos acumulados

## Consideraciones de Rendimiento

- **Paginación**: Implementada en tablas grandes
- **Cache**: React Query para datos frecuentemente accedidos
- **Lazy Loading**: Componentes cargados bajo demanda
- **Optimistic Updates**: Para mejor UX en operaciones CRUD

## Mantenimiento y Extensibilidad

- **Modular**: Cada funcionalidad en componentes separados
- **Tipado**: TypeScript estricto en frontend y backend
- **Testing**: Estructura preparada para pruebas unitarias y E2E
- **Documentación**: JSDoc en funciones críticas
- **Logs**: Sistema de logging para debugging y monitoreo
