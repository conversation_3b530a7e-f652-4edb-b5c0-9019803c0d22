import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsUUID,
  IsEnum,
  IsOptional,
  IsNumber,
} from 'class-validator';

export class CreateRetoManualDto {
  @ApiProperty({
    description: 'Challenge title',
    example: 'Define el proceso de Gestión de Pedidos',
  })
  @IsString()
  titulo: string;

  @ApiProperty({
    description: 'Challenge description',
    example:
      'Describe detalladamente cómo se gestiona un pedido desde su recepción hasta su entrega',
    required: false,
  })
  @IsOptional()
  @IsString()
  descripcion?: string;

  @ApiProperty({
    description: 'Challenge type',
    enum: [
      'definir_proceso',
      'responder_pregunta',
      'definir_tarea',
      'definir_duracion_proceso',
    ],
    example: 'definir_proceso',
  })
  @IsEnum([
    'definir_proceso',
    'responder_pregunta',
    'definir_tarea',
    'definir_duracion_proceso',
  ])
  tipo_accion:
    | 'definir_proceso'
    | 'responder_pregunta'
    | 'definir_tarea'
    | 'definir_duracion_proceso';

  @ApiProperty({
    description: 'Points reward for completing the challenge',
    example: 100,
  })
  @IsNumber()
  puntos_recompensa: number;

  @ApiProperty({
    description: 'Array of user IDs to assign the challenge to',
    type: [String],
    example: [
      'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
      'c3d4e5f6-a7b8-9012-3456-7890abcdef12',
    ],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  usuario_ids: string[];

  @ApiProperty({
    description: 'Related entity ID (process, question, task)',
    example: 'e5f6a7b8-c9d0-1234-5678-90abcdef1234',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  entidad_relacionada_id?: string;

  @ApiProperty({
    description: 'Related entity type',
    enum: ['proceso_cliente', 'pregunta_cliente', 'tarea_cliente'],
    example: 'proceso_cliente',
    required: false,
  })
  @IsOptional()
  @IsEnum(['proceso_cliente', 'pregunta_cliente', 'tarea_cliente'])
  entidad_relacionada_tipo?:
    | 'proceso_cliente'
    | 'pregunta_cliente'
    | 'tarea_cliente';
}

export class CreateRetoManualResponseDto {
  @ApiProperty({
    description: 'Number of challenges created',
    example: 2,
  })
  retos_creados: number;

  @ApiProperty({
    description: 'Array of created challenge IDs',
    type: [String],
    example: [
      'f6a7b8c9-d0e1-2345-6789-0abcdef12345',
      'a7b8c9d0-e1f2-3456-789a-bcdef1234567',
    ],
  })
  reto_ids: string[];

  @ApiProperty({
    description: 'Success message',
    example: 'Retos creados y asignados exitosamente',
  })
  message: string;
}
