import React from 'react';
import { useAuthStore } from '@/features/auth/store/useAuthStore';
import { useManagerDashboard } from '../hooks/useManagerDashboard';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EnhancedKpiCard } from './EnhancedKpiCard';
import { Pie<PERSON>hart, LineChart } from '@/components/ui/charts';
import { RankingList } from '@/components/ui/RankingList';
import { ChallengeCard } from '@/components/ui/ChallengeCard';
import { Link } from 'react-router-dom';

// Icons (you can replace these with actual icon components)
const ProcessIcon = () => <span className="text-2xl">📋</span>;
const FindingIcon = () => <span className="text-2xl">🔍</span>;
const UserIcon = () => <span className="text-2xl">👥</span>;
const ChallengeIcon = () => <span className="text-2xl">🎯</span>;

export const ManagerDashboardLayout: React.FC = () => {
  const user = useAuthStore((state) => state.user);
  const { data: dashboardData, isLoading, isError } = useManagerDashboard();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error al cargar el dashboard</h3>
          <p className="text-red-600 text-sm mt-1">
            No se pudo cargar la información del dashboard. Por favor, intenta de nuevo.
          </p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return null;
  }

  const iconMap = {
    'Procesos Identificados': <ProcessIcon />,
    'Hallazgos Detectados': <FindingIcon />,
    'Usuarios Activos': <UserIcon />,
    'Retos Completados': <ChallengeIcon />,
  };

  return (
    <div className="p-4 md:p-8 bg-gray-50 min-h-screen">
      {/* Header */}
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">
          Dashboard Gerencial
        </h1>
        <p className="text-gray-600 mt-2">
          Bienvenido, {user?.nombre}. Aquí tienes una visión completa de tu empresa.
        </p>
      </header>

      {/* Navigation Menu */}
      <nav className="mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Navegación</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

            {/* Dashboard Link */}
            <Link
              to="/inicio"
              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">🏠</span>
              <div>
                <h4 className="font-medium text-gray-900">Dashboard Principal</h4>
                <p className="text-sm text-gray-500">Panel de empleado</p>
              </div>
            </Link>

            {/* Manager Dashboard Link */}
            <Link
              to="/portal/dashboard"
              className="flex items-center p-3 rounded-lg border border-blue-300 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              <span className="text-2xl mr-3">📊</span>
              <div>
                <h4 className="font-medium text-blue-900">Dashboard Gerencial</h4>
                <p className="text-sm text-blue-600">Panel actual</p>
              </div>
            </Link>

            {/* Processes Link */}
            <Link
              to="/portal/procesos"
              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">📋</span>
              <div>
                <h4 className="font-medium text-gray-900">Procesos</h4>
                <p className="text-sm text-gray-500">Explorar procesos</p>
              </div>
            </Link>

            {/* Findings Link */}
            <Link
              to="/portal/hallazgos"
              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mr-3">🔍</span>
              <div>
                <h4 className="font-medium text-gray-900">Hallazgos</h4>
                <p className="text-sm text-gray-500">Explorar hallazgos</p>
              </div>
            </Link>

          </div>
        </div>
      </nav>

      {/* KPIs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {dashboardData.kpis.map((kpi, index) => (
          <EnhancedKpiCard
            key={index}
            {...kpi}
            icon={iconMap[kpi.title as keyof typeof iconMap]}
          />
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <PieChart
          data={dashboardData.processesByDepartment}
          title="Procesos por Departamento"
          height={350}
        />
        <PieChart
          data={dashboardData.findingsByType}
          title="Hallazgos por Tipo"
          height={350}
        />
      </div>

      {/* Activity Chart */}
      <div className="mb-8">
        <LineChart
          data={dashboardData.recentActivity}
          title="Actividad Reciente (Últimos 7 días)"
          height={300}
          color="#3B82F6"
        />
      </div>

      {/* Bottom Grid: Ranking and Manager Challenge */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performers */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Top Performers
          </h3>
          <RankingList
            users={dashboardData.topPerformers.map(performer => ({
              id: performer.id,
              name: performer.nombre,
              points: performer.puntos,
              departamento: performer.departamento
            }))}
            currentUserId={user?.id || ''}
          />
        </div>

        {/* Manager's Personal Challenge */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Tu Reto Personal
          </h3>
          {dashboardData.managerChallenge ? (
            <ChallengeCard
              title={dashboardData.managerChallenge.titulo}
              description={dashboardData.managerChallenge.descripcion}
              points={dashboardData.managerChallenge.puntos_recompensa}
              status={dashboardData.managerChallenge.estado === 'pendiente' ? 'pending' : 'completed'}
              onAction={() => {
                // Handle challenge action
                console.log('Challenge action clicked');
              }}
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No tienes retos pendientes en este momento.</p>
              <p className="text-sm mt-2">¡Excelente trabajo manteniendo todo al día!</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Acciones Rápidas
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={() => window.location.href = '/portal/procesos'}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="flex items-center">
              <ProcessIcon />
              <div className="ml-3">
                <h4 className="font-medium text-gray-900">Explorar Procesos</h4>
                <p className="text-sm text-gray-500">Ver todos los procesos identificados</p>
              </div>
            </div>
          </button>
          
          <button 
            onClick={() => window.location.href = '/portal/hallazgos'}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="flex items-center">
              <FindingIcon />
              <div className="ml-3">
                <h4 className="font-medium text-gray-900">Ver Hallazgos</h4>
                <p className="text-sm text-gray-500">Revisar oportunidades de mejora</p>
              </div>
            </div>
          </button>
          
          <button 
            onClick={() => window.location.href = '/portal/ranking'}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="flex items-center">
              <UserIcon />
              <div className="ml-3">
                <h4 className="font-medium text-gray-900">Ranking Completo</h4>
                <p className="text-sm text-gray-500">Ver el ranking de todos los usuarios</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};
