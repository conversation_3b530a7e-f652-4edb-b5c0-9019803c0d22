import { useMutation, useQueryClient } from '@tanstack/react-query';
import { crearFuenteInformacion } from '@/lib/api';
import { useToastStore } from '@/store/toast';

export const useCrearFuenteInformacion = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: (data: {
      proceso_cliente_id: string;
      nombre_informacion: string;
      descripcion: string;
      persona_id: string;
      formato: string;
      url_adjunto?: string;
    }) => crearFuenteInformacion(data),
    onSuccess: () => {
      showToast('Fuente de información creada correctamente', 'success');
      queryClient.invalidateQueries({
        queryKey: ['procesos-fuentes-informacion'],
      });
    },
    onError: (error) => {
      showToast(
        `Error al crear la fuente de información: ${error.message}`,
        'error',
      );
    },
  });
};