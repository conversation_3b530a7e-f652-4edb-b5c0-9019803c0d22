import { Injectable } from '@nestjs/common';
import { GamificationService } from 'src/shared/gamification.service';
import { AwardPointsDto } from './dto/award-points.dto';

@Injectable()
export class SystemService {
  constructor(private readonly gamificationService: GamificationService) {}

  async awardPoints(awardPointsDto: AwardPointsDto) {
    const { userId, subtaskId } = awardPointsDto;

    // For now, we'll use a fixed number of points.
    // In the future, this could be determined by the 'action' parameter.
    const points = 10;

    await this.gamificationService.addPoints(userId, points);

    // We also need to mark the subtask as complete
    await this.gamificationService.completeSubtask(userId, subtaskId);
  }
}
