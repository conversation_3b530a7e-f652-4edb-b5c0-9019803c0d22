import {
  Body,
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../features/auth/guards/jwt-auth.guard';
import { GrabacionesService } from './grabaciones.service';
import { CreateGrabacionDto } from './dto/create-grabacion.dto';

// Interface for uploaded file to resolve typing issues
interface UploadedFile {
  originalname: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

@ApiTags('grabaciones')
@Controller('grabaciones')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class GrabacionesController {
  constructor(private readonly grabacionesService: GrabacionesService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a recording file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File upload with metadata',
    type: CreateGrabacionDto,
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid file or data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing JWT token',
  })
  uploadFile(
    @UploadedFile() file: UploadedFile,
    @Body() body: CreateGrabacionDto,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // Validate file type
    const allowedMimeTypes = [
      'video/mp4',
      'video/webm',
      'video/quicktime',
      'audio/mp3',
      'audio/wav',
      'audio/mpeg',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Invalid file type. Only video and audio files are allowed.',
      );
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size exceeds 50MB limit');
    }

    return this.grabacionesService.create(file, body);
  }
}
