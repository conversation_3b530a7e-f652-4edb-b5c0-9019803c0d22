import { Injectable, UnauthorizedException } from '@nestjs/common';
import { DatabaseService } from '../../common/database.service';
import { LoginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';
import { LoginResponseDto, UserProfileDto } from './dto/login-response.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const { email, password } = loginDto;
    const supabase = this.databaseService.getClient();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new UnauthorizedException(error.message);
    }

    const { data: userProfile, error: profileError } = await supabase
      .from('usuarios')
      .select('*')
      .eq('id', data.user.id)
      .single<UserProfileDto>();

    if (profileError) {
      throw new UnauthorizedException(profileError.message);
    }

    const payload = { email: data.user.email, sub: data.user.id };
    return {
      access_token: this.jwtService.sign(payload),
      user: userProfile,
    };
  }
}
