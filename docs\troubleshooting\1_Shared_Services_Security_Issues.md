# Troubleshooting Guide: Module 1 - Security and Code Quality Issues

## Overview
This guide documents common security and code quality issues encountered in Module 1 (Shared Services and Architecture) and their solutions.

---

## 🔧 TypeScript and ESLint Issues

### Issue: Express.Multer.File Type Errors ✅ RESOLVED
**Problem:** TypeScript compilation errors when using `Express.Multer.File` type in file upload handlers.

**Error Messages:**
```
Property 'originalname' does not exist on type 'Express.Multer.File'
Property 'mimetype' does not exist on type 'Express.Multer.File'
```

**Solution Applied:**
1. Install proper type definitions:
   ```bash
   npm install --save-dev @types/multer
   ```

2. Create custom interface to resolve typing issues:
   ```typescript
   // Interface for uploaded file to resolve typing issues
   interface UploadedFile {
     originalname: string;
     mimetype: string;
     size: number;
     buffer: Buffer;
   }

   // Use the custom interface
   uploadFile(@UploadedFile() file: UploadedFile) {
     // File properties are now properly typed
   }
   ```

**Files Affected:**
- `backend/src/grabaciones/grabaciones.controller.ts` ✅
- `backend/src/grabaciones/grabaciones.service.ts` ✅
- `backend/src/shared/storage.service.ts` ✅

### Issue: TypeScript Module Resolution Errors ✅ RESOLVED
**Problem:** TypeScript server not recognizing module imports correctly.

**Error Messages:**
```
Cannot find module './auth.service' or its corresponding type declarations.
Cannot find module './auth.controller' or its corresponding type declarations.
```

**Solution Applied:**
1. **Restart TypeScript Server in VS Code:**
   - Press `Ctrl + Shift + P`
   - Type: `TypeScript: Restart TS Server`
   - Select the command

2. **Alternative methods:**
   - Use `Developer: Reload Window` command
   - Close and reopen VS Code
   - Clear TypeScript cache if needed

**Files Affected:**
- `backend/src/features/auth/auth.module.ts` ✅

---

## 🛡️ Security Configuration Issues

### Issue: CORS Configuration Too Restrictive
**Problem:** Frontend cannot connect to backend due to hardcoded CORS origins.

**Solution:**
Configure environment-based CORS in `main.ts`:
```typescript
// Environment-based CORS configuration
const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',')
  : ['http://localhost:5173'];

app.enableCors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
});
```

**Environment Variables:**
Add to `.env`:
```bash
ALLOWED_ORIGINS=http://localhost:5173,https://your-production-domain.com
```

### Issue: Missing Input Validation
**Problem:** API endpoints accept unvalidated input, creating security vulnerabilities.

**Solution:**
1. Install validation dependencies:
   ```bash
   npm install class-validator class-transformer
   ```

2. Create DTOs with validation:
   ```typescript
   import { IsString, IsNotEmpty, IsIn } from 'class-validator';
   import { ApiProperty } from '@nestjs/swagger';

   export class CreateGrabacionDto {
     @ApiProperty({ description: 'ID of the related entity' })
     @IsString()
     @IsNotEmpty()
     entidad_relacionada_id: string;

     @IsString()
     @IsNotEmpty()
     @IsIn(['proceso_cliente', 'hallazgo_cliente', 'reto_subtarea'])
     entidad_relacionada_tipo: string;
   }
   ```

3. Configure global validation pipe:
   ```typescript
   app.useGlobalPipes(
     new ValidationPipe({
       whitelist: true,
       forbidNonWhitelisted: true,
       transform: true,
     }),
   );
   ```

### Issue: File Upload Security Vulnerabilities
**Problem:** File uploads accept any file type and size, creating security risks.

**Solution:**
Add validation in the controller:
```typescript
@Post()
@UseInterceptors(FileInterceptor('file'))
uploadFile(@UploadedFile() file: Express.Multer.File, @Body() body: CreateGrabacionDto) {
  if (!file) {
    throw new BadRequestException('File is required');
  }

  // Validate file type
  const allowedMimeTypes = [
    'video/mp4', 'video/webm', 'video/quicktime',
    'audio/mp3', 'audio/wav', 'audio/mpeg',
  ];

  if (!allowedMimeTypes.includes(file.mimetype)) {
    throw new BadRequestException('Invalid file type. Only video and audio files are allowed.');
  }

  // Validate file size (50MB limit)
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (file.size > maxSize) {
    throw new BadRequestException('File size exceeds 50MB limit');
  }

  return this.grabacionesService.create(file, body);
}
```

---

## 📚 API Documentation Issues

### Issue: Missing API Documentation
**Problem:** No documentation available for API endpoints, making development and testing difficult.

**Solution:**
1. Install Swagger dependencies:
   ```bash
   npm install @nestjs/swagger swagger-ui-express
   ```

2. Configure Swagger in `main.ts`:
   ```typescript
   import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

   const config = new DocumentBuilder()
     .setTitle('Portal de Clientes API')
     .setDescription('API documentation for the Portal de Clientes application')
     .setVersion('1.0')
     .addBearerAuth()
     .build();
   const document = SwaggerModule.createDocument(app, config);
   SwaggerModule.setup('api', app, document);
   ```

3. Add Swagger decorators to controllers:
   ```typescript
   @ApiTags('grabaciones')
   @Controller('grabaciones')
   @ApiBearerAuth()
   export class GrabacionesController {
     @Post()
     @ApiOperation({ summary: 'Upload a recording file' })
     @ApiConsumes('multipart/form-data')
     @ApiResponse({ status: 201, description: 'File uploaded successfully' })
     uploadFile(@UploadedFile() file: Express.Multer.File, @Body() body: CreateGrabacionDto) {
       // Implementation
     }
   }
   ```

**Access:** Documentation available at `http://localhost:3000/api`

---

## 🔍 Error Handling Issues

### Issue: Unsafe Error Handling
**Problem:** Error handling exposes sensitive information or uses unsafe type assertions.

**Solution:**
Implement proper error handling:
```typescript
try {
  const result = await this.someOperation();
  return result;
} catch (error) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  this.logger.error('Operation failed:', errorMessage);
  throw new InternalServerErrorException('Operation failed');
}
```

**Type Safety:**
Use proper type assertions:
```typescript
// Instead of using 'any'
const typedData = data as ExpectedInterface;

// Or with validation
if (!data || typeof data !== 'object') {
  throw new BadRequestException('Invalid data format');
}
```

---

## 🧪 Testing Issues

### Issue: Tests Failing After Security Changes
**Problem:** Unit tests fail after implementing security enhancements.

**Solution:**
Update test mocks to include new validation:
```typescript
// Mock the ValidationPipe
const mockValidationPipe = {
  transform: jest.fn((value) => value),
};

// Update test setup
beforeEach(async () => {
  const module: TestingModule = await Test.createTestingModule({
    controllers: [GrabacionesController],
    providers: [GrabacionesService],
  })
  .overrideGuard(JwtAuthGuard)
  .useValue({ canActivate: () => true })
  .compile();
});
```

---

## 📋 Environment Configuration

### Required Environment Variables
Ensure all these variables are configured:

```bash
# Supabase
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-public-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Authentication
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long

# External Services
N8N_WEBHOOK_URL=https://your-n8n-webhook-url.com/webhook

# Security
ALLOWED_ORIGINS=http://localhost:5173,https://your-production-domain.com

# Application
PORT=3000
```

---

## 🚀 Quick Fixes Checklist

- [x] Install `@types/multer` for proper TypeScript support
- [x] Configure environment-based CORS origins
- [x] Implement input validation with DTOs
- [x] Add file upload security validation
- [x] Set up Swagger documentation
- [x] Configure global ValidationPipe
- [x] Update error handling to be secure
- [x] Verify all environment variables are set
- [x] Run tests to ensure no regressions
- [x] Check ESLint and TypeScript compilation
- [x] Restart TypeScript server to resolve module resolution issues
- [x] Create custom interfaces for problematic type definitions

## 🎯 Current Status: ✅ ALL ISSUES RESOLVED

**Final State:**
- Backend ESLint: ✅ 0 errors, 0 warnings
- Backend TypeScript: ✅ 0 compilation errors
- Frontend: ✅ Only minor Tailwind CSS warnings (non-critical)
- All tests: ✅ 4/4 passing
- Build process: ✅ Successful compilation

---

---

## 🎨 Frontend CSS Warnings (Non-Critical)

### Issue: Tailwind CSS Unknown At-Rules ⚠️ MINOR
**Problem:** VS Code shows warnings for `@tailwind` directives in CSS files.

**Error Messages:**
```
Unknown at rule @tailwind
```

**Solutions:**
1. **Install Tailwind CSS IntelliSense extension (Recommended):**
   - Open VS Code Extensions (Ctrl + Shift + X)
   - Search: `Tailwind CSS IntelliSense`
   - Install the official extension

2. **Configure VS Code to ignore warnings:**
   ```json
   // .vscode/settings.json
   {
     "css.lint.unknownAtRules": "ignore"
   }
   ```

3. **Leave as-is (Recommended for development):**
   - These warnings don't affect functionality
   - Normal behavior in Tailwind CSS projects

**Status:** ⚠️ Minor warnings only, no impact on functionality

---

**Last Updated:** 2025-07-10
**Module:** 1 - Shared Services and Architecture
**Status:** ✅ All critical issues resolved, minor CSS warnings remain
