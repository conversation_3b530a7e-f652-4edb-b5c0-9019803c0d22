import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { login } from '../../../lib/api';
import { useAuthStore } from '../store/useAuthStore';
import { useToastStore } from '../../../store/toast';

export const useLogin = () => {
  const { setToken, setUser } = useAuthStore();
  const { showToast } = useToastStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: login,
    onSuccess: (data) => {
      setToken(data.access_token);
      setUser(data.user);
      queryClient.invalidateQueries(); // Invalidate all queries to refetch data
      showToast('¡Bienvenido! Has iniciado sesión correctamente.', 'success');
      navigate('/inicio');
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Error al iniciar sesión. Verifica tus credenciales.';
      showToast(errorMessage, 'error');
    },
  });
};

export const useLogout = () => {
  const { logout } = useAuthStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const performLogout = () => {
    logout();
    queryClient.clear(); // Clear all query cache
    navigate('/login');
  };

  return performLogout;
};