import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Usuarios } from '../../../common/database.types';

/**
 * Guard that ensures only users with 'admin_aceleralia' role can access protected routes
 * This guard is specifically for the Aceleralia Admin Panel (Module 5)
 */
@Injectable()
export class AdminRoleGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const request = context.switchToHttp().getRequest();
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    const user: Usuarios = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (user.rol !== 'admin_aceleralia') {
      throw new ForbiddenException('Access denied. Admin role required.');
    }

    return true;
  }
}
