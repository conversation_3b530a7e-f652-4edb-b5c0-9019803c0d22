import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from 'src/common/database.service';
import { UpdateDuracionDto } from './dto/update-duracion.dto';
import { TableTypes } from 'src/common/database.types';

type ProcesoCliente = TableTypes['procesos_clientes']['select'];

@Injectable()
export class ProcesosClientesService {
  constructor(private readonly databaseService: DatabaseService) {}

  async updateDuracion(
    id: string,
    updateDuracionDto: UpdateDuracionDto,
  ): Promise<ProcesoCliente> {
    const response = await this.databaseService
      .getClient()
      .from('procesos_clientes')
      .update(updateDuracionDto)
      .eq('id', id)
      .select()
      .single<ProcesoCliente>();

    const data = response.data;
    const error = response.error;

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(`Proceso con ID ${id} no encontrado.`);
      }
      throw new Error(error.message);
    }

    if (!data) {
      throw new NotFoundException(`Proceso con ID ${id} no encontrado.`);
    }

    return data;
  }
}
