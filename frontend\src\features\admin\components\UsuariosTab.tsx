import { useState } from 'react';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { useCreateUsuario, useVincularPersona } from '../hooks/useCreateUsuario';
import { usePersonaDetalle } from '../hooks/usePersonaDetalle';

interface UsuarioPersona {
  usuario_id: string;
  persona_id: string;
  nombre_completo: string;
  cargo: string;
  departamento: string;
  rol: string;
  puntos_totales: number;
  retos_completados: number;
  numero_retos_diario: number;
  procesos_asignados: number;
  hallazgos_reportados: number;
}

interface PersonaSinUsuario {
  id: string;
  nombre_completo: string;
  cargo: string;
  departamento: string;
}

interface UsuariosTabProps {
  empresaId: string;
  usuariosPersonas: UsuarioPersona[];
  personasSinUsuario: PersonaSinUsuario[];
}

/**
 * Users and Persons tab component
 * Manages user creation and person linking with detailed views
 */
export const UsuariosTab = ({ empresaId, usuariosPersonas, personasSinUsuario }: UsuariosTabProps) => {
  const [showCreateModal, setShowCreateModal] = useState(false);

  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedPersonaId, setSelectedPersonaId] = useState<string>('');
  const [createdUserId, setCreatedUserId] = useState<string>('');
  const [createStep, setCreateStep] = useState<'create' | 'link'>('create');

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rol: 'cliente_empleado' as 'cliente_gerente' | 'cliente_empleado',
    nombre: '',
    apellidos: '',
  });

  const createUsuarioMutation = useCreateUsuario();
  const vincularPersonaMutation = useVincularPersona();
  const { data: personaDetalle } = usePersonaDetalle(selectedPersonaId);

  const handleCreateUsuario = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await createUsuarioMutation.mutateAsync({
      ...formData,
      empresa_id: empresaId,
    });

    setCreatedUserId(result.id);
    setCreateStep('link');
  };

  const handleVincularPersona = async (personaId: string) => {
    await vincularPersonaMutation.mutateAsync({
      personaId,
      data: { usuario_id: createdUserId },
    });

    // Reset modal state
    setShowCreateModal(false);
    setCreateStep('create');
    setCreatedUserId('');
    setFormData({
      email: '',
      password: '',
      rol: 'cliente_empleado',
      nombre: '',
      apellidos: '',
    });
  };

  const handleShowDetail = (personaId: string) => {
    setSelectedPersonaId(personaId);
    setShowDetailModal(true);
  };

  const handleUpdateRetosDialio = async (usuarioId: string, newValue: number) => {
    // This would need an API endpoint to update numero_retos_diario
    // For now, we'll just show a placeholder
    console.log('Update retos diario:', usuarioId, newValue);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Usuarios y Personas</h2>
        <Button onClick={() => setShowCreateModal(true)}>
          Crear Nuevo Usuario
        </Button>
      </div>

      {/* Users and Persons Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Usuarios Activos</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nombre
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cargo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Puntos
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Retos Completados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Retos Diarios
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {usuariosPersonas.map((up) => (
                <tr key={up.usuario_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{up.nombre_completo}</div>
                    <div className="text-sm text-gray-500">{up.departamento}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {up.cargo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-blue-600">{up.puntos_totales}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {up.retos_completados}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={up.numero_retos_diario}
                      onChange={(e) => handleUpdateRetosDialio(up.usuario_id, parseInt(e.target.value))}
                      className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleShowDetail(up.persona_id)}
                    >
                      Ver Detalle
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {usuariosPersonas.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No hay usuarios registrados
          </div>
        )}
      </div>

      {/* Create User Modal - Step 1: Create User */}
      {showCreateModal && createStep === 'create' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Crear Nuevo Usuario</h3>
            <form onSubmit={handleCreateUsuario} className="space-y-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="password">Contraseña Inicial</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  minLength={8}
                  required
                />
              </div>
              <div>
                <Label htmlFor="nombre">Nombre</Label>
                <Input
                  id="nombre"
                  type="text"
                  value={formData.nombre}
                  onChange={(e) => setFormData({ ...formData, nombre: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="apellidos">Apellidos</Label>
                <Input
                  id="apellidos"
                  type="text"
                  value={formData.apellidos}
                  onChange={(e) => setFormData({ ...formData, apellidos: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="rol">Rol</Label>
                <select
                  id="rol"
                  value={formData.rol}
                  onChange={(e) => setFormData({ ...formData, rol: e.target.value as 'cliente_gerente' | 'cliente_empleado' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="cliente_empleado">Cliente Empleado</option>
                  <option value="cliente_gerente">Cliente Gerente</option>
                </select>
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={createUsuarioMutation.isPending}
                >
                  {createUsuarioMutation.isPending ? 'Creando...' : 'Crear Usuario'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Create User Modal - Step 2: Link to Person */}
      {showCreateModal && createStep === 'link' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Usuario creado con éxito</h3>
            <p className="text-gray-600 mb-4">
              Ahora, vincúlalo a una persona existente:
            </p>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {personasSinUsuario.map((persona) => (
                <div
                  key={persona.id}
                  onClick={() => handleVincularPersona(persona.id)}
                  className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                >
                  <div className="font-medium text-gray-900">{persona.nombre_completo}</div>
                  <div className="text-sm text-gray-500">{persona.cargo} - {persona.departamento}</div>
                </div>
              ))}
            </div>
            {personasSinUsuario.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                No hay personas disponibles para vincular
              </div>
            )}
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateModal(false);
                  setCreateStep('create');
                }}
              >
                Cerrar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Person Detail Modal */}
      {showDetailModal && personaDetalle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-medium text-gray-900">
                Detalle de {personaDetalle.persona.nombre_completo}
              </h3>
              <Button
                variant="outline"
                onClick={() => setShowDetailModal(false)}
              >
                Cerrar
              </Button>
            </div>

            {/* Person Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Información Personal</h4>
                <div className="space-y-1 text-sm">
                  <div><span className="font-medium">Cargo:</span> {personaDetalle.persona.cargo}</div>
                  <div><span className="font-medium">Departamento:</span> {personaDetalle.persona.departamento}</div>
                  {personaDetalle.persona.email && (
                    <div><span className="font-medium">Email:</span> {personaDetalle.persona.email}</div>
                  )}
                  {personaDetalle.persona.telefono && (
                    <div><span className="font-medium">Teléfono:</span> {personaDetalle.persona.telefono}</div>
                  )}
                </div>
              </div>

              {personaDetalle.usuario && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Información de Usuario</h4>
                  <div className="space-y-1 text-sm">
                    <div><span className="font-medium">Email:</span> {personaDetalle.usuario.email}</div>
                    <div><span className="font-medium">Rol:</span> {personaDetalle.usuario.rol}</div>
                    <div><span className="font-medium">Puntos:</span> {personaDetalle.usuario.puntos}</div>
                    <div><span className="font-medium">Racha:</span> {personaDetalle.usuario.racha_actual} días</div>
                  </div>
                </div>
              )}
            </div>

            {/* KPIs */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">{personaDetalle.kpis.procesos_asignados}</div>
                <div className="text-sm text-gray-600">Procesos Asignados</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">{personaDetalle.kpis.hallazgos_reportados}</div>
                <div className="text-sm text-gray-600">Hallazgos Reportados</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">{personaDetalle.kpis.retos_completados}</div>
                <div className="text-sm text-gray-600">Retos Completados</div>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">{personaDetalle.kpis.puntos_acumulados}</div>
                <div className="text-sm text-gray-600">Puntos Acumulados</div>
              </div>
            </div>

            {/* Processes and Findings Tables */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Processes */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Procesos Asignados</h4>
                <div className="bg-gray-50 rounded-lg max-h-60 overflow-y-auto">
                  {personaDetalle.procesos.length > 0 ? (
                    <div className="divide-y divide-gray-200">
                      {personaDetalle.procesos.map((proceso) => (
                        <div key={proceso.id} className="p-3">
                          <div className="font-medium text-sm">{proceso.nombre}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {proceso.duracion_minutos_por_ejecucion} min • {proceso.estado_analisis}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      No hay procesos asignados
                    </div>
                  )}
                </div>
              </div>

              {/* Findings */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Hallazgos Reportados</h4>
                <div className="bg-gray-50 rounded-lg max-h-60 overflow-y-auto">
                  {personaDetalle.hallazgos.length > 0 ? (
                    <div className="divide-y divide-gray-200">
                      {personaDetalle.hallazgos.map((hallazgo) => (
                        <div key={hallazgo.id} className="p-3">
                          <div className="font-medium text-sm">{hallazgo.titulo}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {hallazgo.tipo} • {hallazgo.estado}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      No hay hallazgos reportados
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
