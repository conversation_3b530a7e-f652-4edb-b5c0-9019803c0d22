import { useMutation, useQueryClient } from '@tanstack/react-query';
import { responderPregunta } from '@/lib/api';
import { useToastStore } from '@/store/toast';

export const useSubmitRespuesta = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: (data: { pregunta_id: string; respuesta_texto: string }) =>
      responderPregunta(data),
    onSuccess: () => {
      showToast('Respuesta enviada correctamente', 'success');
      queryClient.invalidateQueries({ queryKey: ['preguntas'] });
    },
    onError: (error) => {
      showToast(`Error al enviar la respuesta: ${error.message}`, 'error');
    },
  });
};