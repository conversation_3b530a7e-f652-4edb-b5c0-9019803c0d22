import { ApiProperty } from '@nestjs/swagger';

export class SprintDataDto {
  @ApiProperty({
    description: 'Sprint ID',
    example: 'd4e5f6a7-b8c9-0123-4567-890abcdef123',
  })
  id: string;

  @ApiProperty({
    description: 'Sprint title',
    example: 'Fase 1: Mapeo de Procesos',
  })
  titulo: string;

  @ApiProperty({
    description: 'Sprint start date',
    example: '2024-01-15',
  })
  fecha_inicio: string;

  @ApiProperty({
    description: 'Sprint end date',
    example: '2024-02-15',
  })
  fecha_fin: string;

  @ApiProperty({
    description: 'Sprint status',
    example: 'activa',
  })
  estado: string;

  @ApiProperty({
    description: 'Days elapsed in the sprint',
    example: 10,
  })
  dias_transcurridos: number;

  @ApiProperty({
    description: 'Total days in the sprint',
    example: 31,
  })
  dias_totales: number;

  @ApiProperty({
    description: 'Completed challenges count',
    example: 15,
  })
  retos_completados: number;

  @ApiProperty({
    description: 'Pending challenges count',
    example: 8,
  })
  retos_pendientes: number;
}

export class UsuarioPersonaDto {
  @ApiProperty({
    description: 'User ID',
    example: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
  })
  usuario_id: string;

  @ApiProperty({
    description: 'Person ID',
    example: 'c3d4e5f6-a7b8-9012-3456-7890abcdef12',
  })
  persona_id: string;

  @ApiProperty({
    description: 'Full name',
    example: 'Juan Pérez García',
  })
  nombre_completo: string;

  @ApiProperty({
    description: 'Job position',
    example: 'Analista de Sistemas',
  })
  cargo: string;

  @ApiProperty({
    description: 'Department name',
    example: 'Tecnología',
  })
  departamento: string;

  @ApiProperty({
    description: 'User role',
    example: 'cliente_empleado',
  })
  rol: string;

  @ApiProperty({
    description: 'Total points',
    example: 450,
  })
  puntos_totales: number;

  @ApiProperty({
    description: 'Completed challenges count',
    example: 12,
  })
  retos_completados: number;

  @ApiProperty({
    description: 'Daily challenges number',
    example: 2,
  })
  numero_retos_diario: number;

  @ApiProperty({
    description: 'Assigned processes count',
    example: 3,
  })
  procesos_asignados: number;

  @ApiProperty({
    description: 'Reported findings count',
    example: 5,
  })
  hallazgos_reportados: number;
}

export class GestorPanelResponseDto {
  @ApiProperty({
    description: 'Company information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      nombre: { type: 'string' },
      logo_url: { type: 'string' },
      sector: { type: 'string' },
    },
  })
  empresa: {
    id: string;
    nombre: string;
    logo_url?: string;
    sector?: string;
  };

  @ApiProperty({
    description: 'Active sprint information',
    type: SprintDataDto,
    required: false,
  })
  sprint_activo?: SprintDataDto;

  @ApiProperty({
    description: 'Sprint history',
    type: [SprintDataDto],
  })
  historial_sprints: SprintDataDto[];

  @ApiProperty({
    description: 'Users and persons data',
    type: [UsuarioPersonaDto],
  })
  usuarios_personas: UsuarioPersonaDto[];

  @ApiProperty({
    description: 'Available persons without user account',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        nombre_completo: { type: 'string' },
        cargo: { type: 'string' },
        departamento: { type: 'string' },
      },
    },
  })
  personas_sin_usuario: Array<{
    id: string;
    nombre_completo: string;
    cargo: string;
    departamento: string;
  }>;
}
