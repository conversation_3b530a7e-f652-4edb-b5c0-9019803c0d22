import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../common/database.service';
import { RetosService } from '../retos/retos.service';
import { Usuarios } from 'src/common/database.types';

@Injectable()
export class DashboardService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly retosService: RetosService,
  ) {}

  async getHomeData(user: Usuarios) {
    const dailyChallenges = await this.retosService.findAll(
      user.id,
      'pendiente',
    );

    // TODO: Fetch sprint progress and user KPIs
    const sprintProgress = {
      title: 'Fase 1: Mapeo de Procesos',
      progressPercentage: 50,
    };
    const userKpis = {
      points: user.puntos,
      streak: user.racha_actual,
      completedChallenges: 0, // This will be implemented later
    };

    return {
      dailyChallenges,
      sprint: sprintProgress,
      kpis: userKpis,
    };
  }
}
