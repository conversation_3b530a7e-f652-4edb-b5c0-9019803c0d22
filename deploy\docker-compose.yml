version: '3.8'

services:
  backend:
    build:
      context: ..
      dockerfile: deploy/backend.Dockerfile
    container_name: portal-clientes-backend
    env_file:
      - ../backend/.env
    ports:
      - "3000:3000"
    networks:
      - portal-net

  frontend:
    build:
      context: ..
      dockerfile: deploy/frontend.Dockerfile
    container_name: portal-clientes-frontend
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - portal-net

  proxy:
    image: traefik:v2.10
    container_name: traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8081:8080" # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - portal-net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`localhost`)"
      - "traefik.http.routers.frontend.entrypoints=web"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
      - "traefik.http.routers.backend.rule=Host(`localhost`) && PathPrefix(`/api`)"
      - "traefik.http.routers.backend.entrypoints=web"
      - "traefik.http.services.backend.loadbalancer.server.port=3000"


networks:
  portal-net:
    driver: bridge