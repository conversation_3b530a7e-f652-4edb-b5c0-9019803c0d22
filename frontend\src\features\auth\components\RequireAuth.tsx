import { useLocation, Navigate, Outlet } from 'react-router-dom';
import { useAuthStore } from '../store/useAuthStore';

interface RequireAuthProps {
  allowedRoles: string[];
}

const RequireAuth = ({ allowedRoles }: RequireAuthProps) => {
  const { isAuthenticated, user } = useAuthStore();
  const location = useLocation();

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (user && allowedRoles.includes(user.rol)) {
    return <Outlet />;
  }

  // If the user is authenticated but doesn't have the required role
  return <Navigate to="/unauthorized" state={{ from: location }} replace />;
};

export default RequireAuth;