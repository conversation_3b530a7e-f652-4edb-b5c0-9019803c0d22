import { Routes, Route } from 'react-router-dom';
import DemoPage from './pages/demo';
import DataCaptureDemoPage from './pages/demo/DataCaptureDemoPage';
import LoginPage from './pages/LoginPage';
import { ForgotPasswordPage } from './pages/ForgotPasswordPage';
import { ResetPasswordPage } from './pages/ResetPasswordPage';
import DashboardPage from './pages/DashboardPage';
import RankingPage from './pages/RankingPage';
import AllChallengesPage from './pages/AllChallengesPage';
import ManagerDashboardPage from './pages/manager/ManagerDashboardPage';
import ProcessesPage from './pages/manager/ProcessesPage';
import FindingsPage from './pages/manager/FindingsPage';
import AdminEmpresaSelectorPage from './pages/admin/AdminEmpresaSelectorPage';
import AdminGestorPanelPage from './pages/admin/AdminGestorPanelPage';
import GlobalToast from './components/ui/GlobalToast';
import { useNotifications } from './hooks/useNotifications';
import PersistLogin from './features/auth/components/PersistLogin';
import RequireAuth from './features/auth/components/RequireAuth';
import AuthLayout from './features/auth/components/AuthLayout';

const ROLES = {
  ADMIN_ACELERALIA: 'admin_aceleralia',
  EMPLEADO_ACELERALIA: 'empleado_aceleralia',
  CLIENTE_EMPLEADO: 'cliente_empleado',
  CLIENTE_GERENTE: 'cliente_gerente',
};

function App() {
  useNotifications();

  return (
    <>
      <GlobalToast />
      <Routes>
        <Route path="/login" element={<AuthLayout title="Inicia sesión en tu cuenta" description="Accede a tu panel de control personalizado y gestiona tu cuenta de forma segura"><LoginPage /></AuthLayout>} />
        <Route path="/forgot-password" element={<AuthLayout title="Recuperar Contraseña" description="Introduce tu email para recibir un enlace de recuperación"><ForgotPasswordPage /></AuthLayout>} />
        <Route path="/update-password" element={<AuthLayout title="Actualizar Contraseña" description="Introduce tu nueva contraseña"><ResetPasswordPage /></AuthLayout>} />

        {/* Routes that require login persistence */}
        <Route element={<PersistLogin />}>
          {/* Protected Routes */}
          <Route
            element={<RequireAuth allowedRoles={[ROLES.ADMIN_ACELERALIA, ROLES.EMPLEADO_ACELERALIA, ROLES.CLIENTE_EMPLEADO, ROLES.CLIENTE_GERENTE]} />}
          >
            <Route path="/" element={<div>Home</div>} />
            <Route path="/inicio" element={<DashboardPage />} />
            <Route path="/demo" element={<DemoPage />} />
            <Route path="/demo/data-capture" element={<DataCaptureDemoPage />} />
            <Route path="/portal/ranking" element={<RankingPage />} />
            <Route path="/portal/todos-los-retos" element={<AllChallengesPage />} />
          </Route>

          {/* Manager-only Routes */}
          <Route
            element={<RequireAuth allowedRoles={[ROLES.CLIENTE_GERENTE]} />}
          >
            <Route path="/portal/dashboard" element={<ManagerDashboardPage />} />
            <Route path="/portal/procesos" element={<ProcessesPage />} />
            <Route path="/portal/hallazgos" element={<FindingsPage />} />
          </Route>

          {/* Admin-only Routes */}
          <Route
            element={<RequireAuth allowedRoles={[ROLES.ADMIN_ACELERALIA]} />}
          >
            <Route path="/admin/gestor" element={<AdminEmpresaSelectorPage />} />
            <Route path="/admin/gestor/:empresaId" element={<AdminGestorPanelPage />} />
          </Route>
        </Route>
        
        <Route path="/unauthorized" element={<div>Unauthorized</div>} />

      </Routes>
    </>
  );
}

export default App;
