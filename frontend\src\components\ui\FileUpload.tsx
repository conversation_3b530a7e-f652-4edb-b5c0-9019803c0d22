import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { useUploadGrabacion } from '../../hooks/useUploadGrabacion';

interface FileUploadProps {
  onUploadComplete: () => void;
  data: {
    entidad_relacionada_id: string;
    entidad_relacionada_tipo: string;
    usuario_id: string;
    tipo_grabacion: string;
  };
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadComplete,
  data,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const {
    mutate: upload,
    isPending: isUploading,
    isSuccess,
    isError,
    error,
  } = useUploadGrabacion();

  useEffect(() => {
    if (isSuccess) {
      onUploadComplete();
    }
  }, [isSuccess, onUploadComplete]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
  });

  const handleUpload = () => {
    if (!file) return;
    upload({ file, body: data });
  };

  return (
    <div className="w-full max-w-lg mx-auto">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors duration-300
        ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`}
      >
        <input {...getInputProps()} />
        {file ? (
          <p className="text-gray-700">
            Archivo seleccionado: <span className="font-semibold">{file.name}</span>
          </p>
        ) : (
          <p className="text-gray-500">
            {isDragActive
              ? 'Suelta el archivo aquí...'
              : 'Arrastra y suelta un archivo aquí, o haz clic para seleccionar uno.'}
          </p>
        )}
      </div>

      {file && (
        <div className="mt-4">
          <button
            onClick={handleUpload}
            disabled={isUploading || isSuccess}
            className="w-full px-4 py-2 bg-blue-500 text-white font-bold rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isUploading ? 'Subiendo...' : 'Subir Archivo'}
          </button>
        </div>
      )}

      {isUploading && (
        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-4">
          <div className="bg-blue-600 h-2.5 rounded-full w-full animate-pulse"></div>
        </div>
      )}

      {isSuccess && (
        <p className="mt-4 text-center text-green-600 font-semibold">
          ¡Archivo subido con éxito!
        </p>
      )}

      {isError && (
        <p className="mt-4 text-center text-red-600 font-semibold">
          Error: {error?.message || 'Ocurrió un error desconocido.'}
        </p>
      )}
    </div>
  );
};