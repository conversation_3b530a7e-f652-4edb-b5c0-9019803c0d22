import {
  Controller,
  Get,
  Post,
  Patch,
  Body,
  Param,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { AdminService } from './admin.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminRoleGuard } from './guards/admin-role.guard';
import { EmpresaListResponseDto } from './dto/empresa-list.dto';
import { GestorPanelResponseDto } from './dto/gestor-panel.dto';
import {
  CreateUsuarioDto,
  CreateUsuarioResponseDto,
} from './dto/create-usuario.dto';
import {
  VincularPersonaDto,
  VincularPersonaResponseDto,
} from './dto/vincular-persona.dto';
import {
  CreateSprintDto,
  CreateSprintResponseDto,
} from './dto/create-sprint.dto';
import {
  CreateRetoManualDto,
  CreateRetoManualResponseDto,
} from './dto/create-reto-manual.dto';
import { PersonaDetalleResponseDto } from './dto/persona-detalle.dto';

/**
 * Controller for Aceleralia Admin Panel (Module 5)
 * Protected by JWT authentication and admin role guard
 * Only users with 'admin_aceleralia' role can access these endpoints
 */
@ApiTags('Admin Panel')
@ApiBearerAuth()
@Controller('admin')
@UseGuards(JwtAuthGuard, AdminRoleGuard)
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('empresas')
  @ApiOperation({
    summary: 'Get list of all companies',
    description:
      'Returns list of all client companies for the company selector',
  })
  @ApiResponse({
    status: 200,
    description: 'Companies list retrieved successfully',
    type: EmpresaListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  getEmpresas(): Promise<EmpresaListResponseDto> {
    return this.adminService.getEmpresas();
  }

  @Get('gestor/:empresaId')
  @ApiOperation({
    summary: 'Get comprehensive panel data for a company',
    description:
      'Returns all data needed for the company management panel including sprints, users, and persons',
  })
  @ApiParam({
    name: 'empresaId',
    description: 'Company ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Company panel data retrieved successfully',
    type: GestorPanelResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Company not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  getGestorPanel(
    @Param('empresaId', ParseUUIDPipe) empresaId: string,
  ): Promise<GestorPanelResponseDto> {
    return this.adminService.getGestorPanel(empresaId);
  }

  @Post('usuarios')
  @ApiOperation({
    summary: 'Create a new user account',
    description:
      'Creates a new user in Supabase Auth and links it to the usuarios table',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: CreateUsuarioResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error or user creation failed',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  createUsuario(
    @Body() createUsuarioDto: CreateUsuarioDto,
  ): Promise<CreateUsuarioResponseDto> {
    return this.adminService.createUsuario(createUsuarioDto);
  }

  @Patch('personas/:personaId/vincular')
  @ApiOperation({
    summary: 'Link a user to a person',
    description: 'Links an existing user account to a person record',
  })
  @ApiParam({
    name: 'personaId',
    description: 'Person ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'User linked to person successfully',
    type: VincularPersonaResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Person or user not found',
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - person already has a user or user already linked',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  vincularPersona(
    @Param('personaId', ParseUUIDPipe) personaId: string,
    @Body() vincularPersonaDto: VincularPersonaDto,
  ): Promise<VincularPersonaResponseDto> {
    return this.adminService.vincularPersona(personaId, vincularPersonaDto);
  }

  @Get('personas/:personaId/detalle')
  @ApiOperation({
    summary: 'Get detailed information about a person',
    description:
      'Returns comprehensive data about a person including KPIs, processes, and findings',
  })
  @ApiParam({
    name: 'personaId',
    description: 'Person ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Person details retrieved successfully',
    type: PersonaDetalleResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Person not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  getPersonaDetalle(
    @Param('personaId', ParseUUIDPipe) personaId: string,
  ): Promise<PersonaDetalleResponseDto> {
    return this.adminService.getPersonaDetalle(personaId);
  }

  @Post('sprints')
  @ApiOperation({
    summary: 'Create a new sprint',
    description: 'Creates a new collaboration sprint for a company',
  })
  @ApiResponse({
    status: 201,
    description: 'Sprint created successfully',
    type: CreateSprintResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error',
  })
  @ApiResponse({
    status: 404,
    description: 'Company not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  createSprint(
    @Body() createSprintDto: CreateSprintDto,
  ): Promise<CreateSprintResponseDto> {
    return this.adminService.createSprint(createSprintDto);
  }

  @Post('retos/manual')
  @ApiOperation({
    summary: 'Create manual challenges',
    description: 'Creates and assigns challenges manually to multiple users',
  })
  @ApiResponse({
    status: 201,
    description: 'Challenges created and assigned successfully',
    type: CreateRetoManualResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation error or challenge creation failed',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more users not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied. Admin role required.',
  })
  createRetoManual(
    @Body() createRetoManualDto: CreateRetoManualDto,
  ): Promise<CreateRetoManualResponseDto> {
    return this.adminService.createRetoManual(createRetoManualDto);
  }
}
