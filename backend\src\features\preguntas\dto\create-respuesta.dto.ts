import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateRespuestaDto {
  @ApiProperty({
    description: 'The ID of the question being answered.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsNotEmpty()
  @IsUUID()
  preguntaId: string;

  @ApiProperty({
    description: 'The text of the answer.',
    example: 'The answer to the question is...',
  })
  @IsNotEmpty()
  @IsString()
  respuestaTexto: string;
}
