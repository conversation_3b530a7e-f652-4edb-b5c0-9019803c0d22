import DefinirDuracionForm from '@/features/data-capture/components/DefinirDuracionForm';
import DefinirProcesoPanel from '@/features/data-capture/components/DefinirProcesoPanel';
import InformationSourcesForm from '@/features/data-capture/components/InformationSourcesForm';
import ResponderPreguntaPanel from '@/features/data-capture/components/ResponderPreguntaPanel';

const DataCaptureDemoPage = () => {
  const mockProcesoId = 'a1b2c3d4-e5f6-7890-1234-567890abcdef';
  const mockPreguntaId = 'b2c3d4e5-f6a7-8901-2345-67890abcdef1';

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold mb-8 text-center text-gray-800">
        Data Capture Components Demo
      </h1>
      <div className="space-y-12">
        <div className="p-6 bg-white rounded-xl shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700 border-b pb-2">
            Definir Proceso Panel
          </h2>
          <DefinirProcesoPanel procesoId={mockProcesoId} />
        </div>

        <div className="p-6 bg-white rounded-xl shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700 border-b pb-2">
            Responder Pregunta Panel
          </h2>
          <ResponderPreguntaPanel preguntaId={mockPreguntaId} />
        </div>

        <div className="p-6 bg-white rounded-xl shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700 border-b pb-2">
            Definir Duración Form
          </h2>
          <div className="max-w-md mx-auto">
            <DefinirDuracionForm procesoId={mockProcesoId} />
          </div>
        </div>
        
        <div className="p-6 bg-white rounded-xl shadow-md">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700 border-b pb-2">
            Information Sources Form
          </h2>
          <div className="max-w-2xl mx-auto">
            <InformationSourcesForm procesoId={mockProcesoId} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataCaptureDemoPage;