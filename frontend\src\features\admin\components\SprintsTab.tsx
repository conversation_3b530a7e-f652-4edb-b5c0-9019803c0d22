import { useState } from 'react';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { useCreateSprint } from '../hooks/useCreateSprint';

interface SprintData {
  id: string;
  titulo: string;
  fecha_inicio: string;
  fecha_fin: string;
  estado: string;
  dias_transcurridos: number;
  dias_totales: number;
  retos_completados: number;
  retos_pendientes: number;
}

interface SprintsTabProps {
  empresaId: string;
  sprintActivo?: SprintData;
  historialSprints: SprintData[];
}

/**
 * Sprints and Progress tab component
 * Manages sprint creation and displays progress information
 */
export const SprintsTab = ({ empresaId, sprintActivo, historialSprints }: SprintsTabProps) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [formData, setFormData] = useState({
    titulo: '',
    fecha_inicio: '',
    fecha_fin: '',
  });

  const createSprintMutation = useCreateSprint();

  const handleCreateSprint = async (e: React.FormEvent) => {
    e.preventDefault();
    
    await createSprintMutation.mutateAsync({
      ...formData,
      empresa_id: empresaId,
    });

    setShowCreateModal(false);
    setFormData({ titulo: '', fecha_inicio: '', fecha_fin: '' });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES');
  };

  const getProgressPercentage = (sprint: SprintData) => {
    return Math.min(100, (sprint.dias_transcurridos / sprint.dias_totales) * 100);
  };

  const getCompletionPercentage = (sprint: SprintData) => {
    const total = sprint.retos_completados + sprint.retos_pendientes;
    return total > 0 ? (sprint.retos_completados / total) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      {/* Create Sprint Button */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Sprints y Progreso</h2>
        <Button onClick={() => setShowCreateModal(true)}>
          Crear Nuevo Sprint
        </Button>
      </div>

      {/* Active Sprint Progress */}
      {sprintActivo && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Sprint Activo: {sprintActivo.titulo}
          </h3>
          
          {/* Time Progress */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progreso temporal</span>
              <span>{sprintActivo.dias_transcurridos} / {sprintActivo.dias_totales} días</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage(sprintActivo)}%` }}
              />
            </div>
          </div>

          {/* Challenges Progress */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Progress Donut Chart Placeholder */}
            <div className="text-center">
              <div className="relative inline-flex items-center justify-center w-32 h-32 mb-4">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-gray-200"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="transparent"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-green-600"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="transparent"
                    strokeDasharray={`${getCompletionPercentage(sprintActivo)}, 100`}
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-gray-900">
                    {Math.round(getCompletionPercentage(sprintActivo))}%
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-600">Retos Completados</p>
            </div>

            {/* Stats */}
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Retos Completados:</span>
                <span className="font-medium text-green-600">{sprintActivo.retos_completados}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Retos Pendientes:</span>
                <span className="font-medium text-orange-600">{sprintActivo.retos_pendientes}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fecha Inicio:</span>
                <span className="font-medium">{formatDate(sprintActivo.fecha_inicio)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fecha Fin:</span>
                <span className="font-medium">{formatDate(sprintActivo.fecha_fin)}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sprint History Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Historial de Sprints</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sprint
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duración
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Completado
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {historialSprints.map((sprint) => (
                <tr key={sprint.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{sprint.titulo}</div>
                    <div className="text-sm text-gray-500">
                      {formatDate(sprint.fecha_inicio)} - {formatDate(sprint.fecha_fin)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {sprint.dias_totales} días
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      sprint.estado === 'activa' ? 'bg-green-100 text-green-800' :
                      sprint.estado === 'finalizada' ? 'bg-gray-100 text-gray-800' :
                      sprint.estado === 'planificada' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {sprint.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {Math.round(getCompletionPercentage(sprint))}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {historialSprints.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No hay sprints registrados
          </div>
        )}
      </div>

      {/* Create Sprint Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Crear Nuevo Sprint</h3>
            <form onSubmit={handleCreateSprint} className="space-y-4">
              <div>
                <Label htmlFor="titulo">Título del Sprint</Label>
                <Input
                  id="titulo"
                  type="text"
                  value={formData.titulo}
                  onChange={(e) => setFormData({ ...formData, titulo: e.target.value })}
                  placeholder="Ej: Fase 1: Mapeo de Procesos"
                  required
                />
              </div>
              <div>
                <Label htmlFor="fecha_inicio">Fecha de Inicio</Label>
                <Input
                  id="fecha_inicio"
                  type="date"
                  value={formData.fecha_inicio}
                  onChange={(e) => setFormData({ ...formData, fecha_inicio: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="fecha_fin">Fecha de Fin</Label>
                <Input
                  id="fecha_fin"
                  type="date"
                  value={formData.fecha_fin}
                  onChange={(e) => setFormData({ ...formData, fecha_fin: e.target.value })}
                  required
                />
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={createSprintMutation.isPending}
                >
                  {createSprintMutation.isPending ? 'Creando...' : 'Crear Sprint'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};
