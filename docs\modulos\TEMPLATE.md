# Feature: [Feature Name]

## 1. Overview

*(Briefly describe the purpose and goal of this feature.)*

## 2. Requirements

*(List the specific functional and non-functional requirements. Use bullet points or numbered lists.)*

*   Requirement 1
*   Requirement 2
*   ...

## 3. UI/UX Design

*(Describe the user interface elements, layout, and user experience. Include wireframes, mockups, or links to design files if available.)*

*   **Key Screens/Components:**
    *   [Screen/Component 1]
    *   [Screen/Component 2]
*   **User Flow:**
    *   (Describe the steps a user takes to interact with this feature)

## 4. Technical Details

*   **Frontend:**
    *   Relevant Components: 
    *   State Management: (Contexts, hooks, stores involved)
    *   API Interactions: (Endpoints called, data format)
*   **Backend:**
    *   Relevant API Routes: 
    *   Services Used: 
    *   Database Tables Involved:
    *   External Services: (OpenAI, n8n, etc.)
*   **Data Models:**
    *   (Relevant Pydantic models or TypeScript types)

## 5. Acceptance Criteria

*(Define how to verify that the feature is implemented correctly.)*

*   Given [context], when [action], then [expected outcome].
*   ...

## 6. Notes & Decisions

*(Record any specific implementation notes, decisions made during development, or potential future improvements.)*