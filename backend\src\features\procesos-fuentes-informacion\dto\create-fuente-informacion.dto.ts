import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  IsUrl,
} from 'class-validator';

enum ProcesosFuentesInformacionEstado {
  VALIDO = 'valido',
  PENDIENTE_REVISION = 'pendiente_revision',
  OBSOLETO = 'obsoleto',
}

export class CreateFuenteInformacionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  proceso_cliente_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  nombre_informacion: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  descripcion?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  persona_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  formato: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  url_adjunto?: string;

  @ApiProperty({ enum: ProcesosFuentesInformacionEstado })
  @IsNotEmpty()
  @IsEnum(ProcesosFuentesInformacionEstado)
  estado: ProcesosFuentesInformacionEstado;
}
