import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { DatabaseService } from 'src/common/database.service';
import { N8NWebhookService } from 'src/shared/n8n-webhook.service';
import { StorageService } from 'src/shared/storage.service';
import { CreateGrabacionDto } from './dto/create-grabacion.dto';
import { v4 as uuidv4 } from 'uuid';

// Interface for uploaded file to resolve typing issues
interface UploadedFile {
  originalname: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

export interface Grabacion {
  id: string;
  entidad_relacionada_id: string;
  entidad_relacionada_tipo: string;
  usuario_id: string;
  tipo_grabacion: string;
  url_almacenamiento: string;
  estado_procesamiento: string;
}

@Injectable()
export class GrabacionesService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly storageService: StorageService,
    private readonly n8nWebhookService: N8NWebhookService,
  ) {}

  async create(
    file: UploadedFile,
    body: CreateGrabacionDto,
  ): Promise<Grabacion> {
    if (!file || !file.originalname) {
      throw new InternalServerErrorException('Invalid file provided');
    }

    const supabase = this.databaseService.getClient();
    const fileName = `${uuidv4()}-${file.originalname}`;
    const bucket = 'grabaciones-temporales';

    try {
      const path = await this.storageService.uploadToTemporal(
        file,
        bucket,
        fileName,
      );

      const { data, error } = await supabase
        .from('grabaciones')
        .insert([
          {
            id: uuidv4(),
            ...body,
            url_almacenamiento: path,
            estado_procesamiento: 'pendiente',
          },
        ])
        .select();

      if (error) {
        throw new InternalServerErrorException(
          `Error creating grabacion: ${error.message}`,
        );
      }

      if (!data || data.length === 0) {
        throw new InternalServerErrorException(
          'Failed to create grabacion: No data returned',
        );
      }

      const grabacion = data[0] as Grabacion;

      // Trigger async processing workflow
      await this.n8nWebhookService.triggerWorkflow('procesarVideo', {
        version: '1.0',
        eventType: 'new_recording',
        data: {
          grabacionId: grabacion.id,
          usuarioId: grabacion.usuario_id,
          entidadRelacionadaId: grabacion.entidad_relacionada_id,
          entidadRelacionadaTipo: grabacion.entidad_relacionada_tipo as
            | 'proceso_cliente'
            | 'pregunta_cliente'
            | 'tarea_cliente',
        },
      });

      return grabacion;
    } catch (error) {
      if (error instanceof InternalServerErrorException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Unexpected error creating grabacion: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }
}
