import { useState } from 'react';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { useCreateRetoManual } from '../hooks/useCreateRetoManual';

interface UsuarioPersona {
  usuario_id: string;
  persona_id: string;
  nombre_completo: string;
  cargo: string;
  departamento: string;
  rol: string;
  puntos_totales: number;
  retos_completados: number;
  numero_retos_diario: number;
  procesos_asignados: number;
  hallazgos_reportados: number;
}

interface RetosTabProps {
  usuariosPersonas: UsuarioPersona[];
}

type TipoAccion = 'definir_proceso' | 'responder_pregunta' | 'definir_tarea' | 'definir_duracion_proceso';

/**
 * Manual Challenge Creation tab component
 * Provides powerful tools for creating and assigning challenges
 */
export const RetosTab = ({ usuariosPersonas }: RetosTabProps) => {
  const [formData, setFormData] = useState({
    titulo: '',
    descripcion: '',
    tipo_accion: 'definir_proceso' as TipoAccion,
    puntos_recompensa: 100,
    usuario_ids: [] as string[],
  });

  const [selectAll, setSelectAll] = useState(false);

  const createRetoMutation = useCreateRetoManual();

  const tipoAccionOptions = [
    { value: 'definir_proceso', label: 'Definir Proceso', placeholder: 'Define el proceso de...' },
    { value: 'responder_pregunta', label: 'Responder Pregunta', placeholder: '¿Cómo se realiza...?' },
    { value: 'definir_tarea', label: 'Definir Tarea', placeholder: 'Define la tarea de...' },
    { value: 'definir_duracion_proceso', label: 'Definir Duración', placeholder: 'Establece la duración del proceso...' },
  ];

  const handleTipoAccionChange = (tipo: TipoAccion) => {
    const option = tipoAccionOptions.find(opt => opt.value === tipo);
    setFormData({
      ...formData,
      tipo_accion: tipo,
      titulo: option?.placeholder || '',
    });
  };

  const handleUsuarioToggle = (usuarioId: string) => {
    const newUsuarioIds = formData.usuario_ids.includes(usuarioId)
      ? formData.usuario_ids.filter(id => id !== usuarioId)
      : [...formData.usuario_ids, usuarioId];
    
    setFormData({ ...formData, usuario_ids: newUsuarioIds });
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setFormData({ ...formData, usuario_ids: [] });
    } else {
      setFormData({ ...formData, usuario_ids: usuariosPersonas.map(up => up.usuario_id) });
    }
    setSelectAll(!selectAll);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.usuario_ids.length === 0) {
      alert('Debes seleccionar al menos un usuario');
      return;
    }

    await createRetoMutation.mutateAsync(formData);

    // Reset form
    setFormData({
      titulo: '',
      descripcion: '',
      tipo_accion: 'definir_proceso',
      puntos_recompensa: 100,
      usuario_ids: [],
    });
    setSelectAll(false);
  };

  const selectedOption = tipoAccionOptions.find(opt => opt.value === formData.tipo_accion);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Creación Manual de Retos</h2>
        <p className="text-gray-600">
          Crea y asigna retos personalizados a los usuarios de la empresa
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Challenge Type Selector */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Tipo de Reto</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tipoAccionOptions.map((option) => (
              <div
                key={option.value}
                onClick={() => handleTipoAccionChange(option.value as TipoAccion)}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  formData.tipo_accion === option.value
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-gray-900">{option.label}</div>
                <div className="text-sm text-gray-500 mt-1">{option.placeholder}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Dynamic Form */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Detalles del Reto</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="titulo">Título del Reto</Label>
              <Input
                id="titulo"
                type="text"
                value={formData.titulo}
                onChange={(e) => setFormData({ ...formData, titulo: e.target.value })}
                placeholder={selectedOption?.placeholder}
                required
              />
            </div>

            <div>
              <Label htmlFor="descripcion">Descripción (Opcional)</Label>
              <textarea
                id="descripcion"
                value={formData.descripcion}
                onChange={(e) => setFormData({ ...formData, descripcion: e.target.value })}
                placeholder="Proporciona instrucciones adicionales o contexto..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <Label htmlFor="puntos_recompensa">Puntos de Recompensa</Label>
              <Input
                id="puntos_recompensa"
                type="number"
                min="1"
                max="1000"
                value={formData.puntos_recompensa}
                onChange={(e) => setFormData({ ...formData, puntos_recompensa: parseInt(e.target.value) })}
                required
              />
            </div>
          </div>
        </div>

        {/* User Assignment Panel */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Asignación de Usuarios</h3>
            <Button
              type="button"
              variant="outline"
              onClick={handleSelectAll}
            >
              {selectAll ? 'Deseleccionar Todos' : 'Seleccionar Todos'}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-60 overflow-y-auto">
            {usuariosPersonas.map((usuario) => (
              <div
                key={usuario.usuario_id}
                className={`p-3 border rounded-lg cursor-pointer transition-all ${
                  formData.usuario_ids.includes(usuario.usuario_id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleUsuarioToggle(usuario.usuario_id)}
              >
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.usuario_ids.includes(usuario.usuario_id)}
                    onChange={() => handleUsuarioToggle(usuario.usuario_id)}
                    className="mr-3"
                    onClick={(e) => e.stopPropagation()}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-sm text-gray-900">
                      {usuario.nombre_completo}
                    </div>
                    <div className="text-xs text-gray-500">
                      {usuario.cargo} • {usuario.departamento}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {usuario.puntos_totales} pts • {usuario.retos_completados} retos
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {usuariosPersonas.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No hay usuarios disponibles para asignar retos
            </div>
          )}

          {formData.usuario_ids.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-800">
                <strong>{formData.usuario_ids.length}</strong> usuario(s) seleccionado(s)
              </div>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={createRetoMutation.isPending || formData.usuario_ids.length === 0}
            className="px-8"
          >
            {createRetoMutation.isPending ? 'Creando Retos...' : 'Crear y Asignar Reto(s)'}
          </Button>
        </div>
      </form>

      {/* Preview Section */}
      {formData.titulo && formData.usuario_ids.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Vista Previa</h3>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex justify-between items-start mb-2">
              <h4 className="font-medium text-gray-900">{formData.titulo}</h4>
              <span className="text-sm font-medium text-blue-600">
                +{formData.puntos_recompensa} pts
              </span>
            </div>
            {formData.descripcion && (
              <p className="text-sm text-gray-600 mb-3">{formData.descripcion}</p>
            )}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Tipo: {selectedOption?.label}</span>
              <span>Asignado a {formData.usuario_ids.length} usuario(s)</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
