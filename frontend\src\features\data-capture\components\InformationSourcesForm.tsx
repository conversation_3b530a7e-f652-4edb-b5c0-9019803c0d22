import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X, Loader2 } from 'lucide-react';
import { useCrearFuenteInformacion } from '../hooks/useCrearFuenteInformacion';
import { getPersonas } from '@/lib/api';
import { useAuthStore } from '@/features/auth/store/useAuthStore';

interface Source {
  id: number;
  name: string;
  description: string;
  persona: string;
  format: string;
  file?: File;
}

interface Persona {
  id: string;
  nombre: string;
  apellidos: string;
}

interface Props {
  procesoId: string;
}

const InformationSourcesForm = ({ procesoId }: Props) => {
  const [sources, setSources] = useState<Source[]>([
    { id: 1, name: '', description: '', persona: '', format: '' },
  ]);
  const [personas, setPersonas] = useState<Persona[]>([]);
  const user = useAuthStore((state) => state.user);
  const { mutate: crearFuente, isPending } = useCrearFuenteInformacion();

  useEffect(() => {
    if (user?.empresa_id) {
      getPersonas(user.empresa_id).then(setPersonas);
    }
  }, [user?.empresa_id]);

  const handleAddSource = () => {
    setSources([
      ...sources,
      { id: Date.now(), name: '', description: '', persona: '', format: '' },
    ]);
  };

  const handleRemoveSource = (id: number) => {
    setSources(sources.filter((source) => source.id !== id));
  };

  const handleInputChange = (
    id: number,
    field: keyof Source,
    value: string | File,
  ) => {
    setSources(
      sources.map((source) =>
        source.id === id ? { ...source, [field]: value } : source,
      ),
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sources.forEach((source) => {
      crearFuente({
        proceso_cliente_id: procesoId,
        nombre_informacion: source.name,
        descripcion: source.description,
        persona_id: source.persona,
        formato: source.format,
        // TODO: Handle file upload for url_adjunto
      });
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-4 border rounded-lg shadow-sm bg-white">
      <h3 className="text-lg font-medium text-gray-800">Fuentes de Información</h3>
      <div className="space-y-4">
        {sources.map((source) => (
          <div key={source.id} className="p-4 border rounded-md relative">
            {sources.length > 1 && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => handleRemoveSource(source.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor={`name-${source.id}`}>Nombre</Label>
                <Input
                  id={`name-${source.id}`}
                  value={source.name}
                  onChange={(e) => handleInputChange(source.id, 'name', e.target.value)}
                  placeholder="Ej: Reporte de Ventas"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`description-${source.id}`}>Descripción</Label>
                <Input
                  id={`description-${source.id}`}
                  value={source.description}
                  onChange={(e) =>
                    handleInputChange(source.id, 'description', e.target.value)
                  }
                  placeholder="Ej: Reporte mensual de ventas"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`persona-${source.id}`}>Persona</Label>
                <select
                  id={`persona-${source.id}`}
                  value={source.persona}
                  onChange={(e) =>
                    handleInputChange(source.id, 'persona', e.target.value)
                  }
                  className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  required
                >
                  <option value="">Selecciona una persona</option>
                  {personas.map((p) => (
                    <option key={p.id} value={p.id}>
                      {p.nombre} {p.apellidos}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor={`format-${source.id}`}>Formato</Label>
                <Input
                  id={`format-${source.id}`}
                  value={source.format}
                  onChange={(e) =>
                    handleInputChange(source.id, 'format', e.target.value)
                  }
                  placeholder="Ej: Excel, PDF"
                  required
                />
              </div>
              <div className="space-y-2 col-span-full">
                <Label htmlFor={`file-${source.id}`}>Adjunto (Opcional)</Label>
                <Input
                  id={`file-${source.id}`}
                  type="file"
                  onChange={(e) =>
                    handleInputChange(source.id, 'file', e.target.files?.[0] || '')
                  }
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="flex justify-between items-center">
        <Button type="button" variant="outline" onClick={handleAddSource}>
          Añadir Fuente
        </Button>
        <Button type="submit" disabled={isPending}>
          {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Guardar Fuentes
        </Button>
      </div>
    </form>
  );
};

export default InformationSourcesForm;