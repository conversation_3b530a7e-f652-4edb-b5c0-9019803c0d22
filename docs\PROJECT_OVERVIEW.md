# Project Overview
Resumen Ejecutivo
El Portal de Clientes de Aceleralia es una plataforma web estratégica de doble propósito, diseñada para revolucionar la forma en que realizamos nuestras consultorías y entregamos valor a nuestros clientes. Por un lado, actúa como un centro de colaboración gamificado que nos permite extraer el conocimiento tácito de los empleados de nuestros clientes de una manera eficiente, atractiva y sin fricciones. Por otro, funciona como un panel de control ejecutivo en tiempo real que proporciona a la gerencia de la empresa cliente una visión 360º y sin precedentes de sus procesos internos, ineficiencias y oportunidades de mejora.
Este proyecto no es simplemente una herramienta, sino la materialización de nuestra filosofía: ser un socio tecnológico integral que acelera el crecimiento empresarial a través de la inteligencia de datos y la optimización de procesos.
1. El Problema Fundamental que Resolvemos
En el corazón de muchas empresas, especialmente en las PYMEs, el conocimiento más valioso sobre la operativa diaria reside de forma tácita en la mente de sus empleados. Este conocimiento no está documentado, estandarizado ni es fácilmente accesible, lo que crea dos problemas críticos:
Para la Empresa Cliente: Genera una alta dependencia de personas clave, dificulta la formación de nuevos empleados, perpetúa ineficiencias y crea "cajas negras" operativas que impiden la optimización y la escalabilidad.
Para Aceleralia: El proceso tradicional de extraer este conocimiento a través de entrevistas y talleres es intensivo en tiempo, costoso y a menudo superficial. Escalar nuestro modelo de consultoría se ve limitado por este cuello de botella humano.
2. La Solución: Un Ecosistema de Colaboración Inteligente
El Portal de Clientes ataca este problema de raíz creando un ecosistema donde la tecnología actúa como un catalizador para transformar el conocimiento tácito en activos de datos estructurados y accionables.
Para el Empleado: Transformamos la tediosa tarea de "documentar" en un "juego" productivo. Reemplazamos los formularios y los documentos de texto por interacciones naturales y guiadas, como grabar la pantalla mientras explican un proceso o responder preguntas con notas de voz.
Para el Gerente: Convertimos la niebla de la operativa diaria en un panel de control claro y visual. Le proporcionamos gráficos, KPIs y herramientas de exploración que le permiten entender su propia empresa como nunca antes.
3. Objetivos Estratégicos Clave
¿Qué queremos conseguir con este portal?
Estandarizar y Escalar Nuestro Proceso de Consultoría: Crear un método repetible y de alta calidad para el diagnóstico y mapeo de procesos, permitiendo a Aceleralia atender a más clientes sin un crecimiento lineal de nuestro personal.
Profundizar en la Calidad de los Datos: Ir más allá de la información superficial de una entrevista inicial, obteniendo descripciones detalladas, visuales y contextuales de los procesos directamente de quienes los ejecutan.
Maximizar el Compromiso (Engagement) del Cliente: Diseñar una experiencia de usuario tan atractiva y gratificante que los empleados de nuestros clientes se conviertan en participantes activos y entusiastas del proceso de mejora.
Demostrar Valor de Forma Continua y Transparente: Ofrecer a la gerencia una ventana en tiempo real al progreso y los descubrimientos de nuestra consultoría, justificando la inversión y construyendo una relación de confianza a largo plazo.
Crear una Base para la Automatización Futura: Generar un conjunto de datos tan rico y bien estructurado (procesos, tareas, duraciones, hallazgos) que sirva como el cimiento perfecto para la futura implementación de soluciones de automatización e Inteligencia Artificial.
4. La Experiencia del Usuario: ¿Cómo Queremos que Sea?
La filosofía de diseño se basa en dos perfiles de usuario con necesidades distintas pero complementarias:
A) Para el Empleado: Un Juego Serio y Productivo
Gamificación Inteligente: El núcleo de la experiencia. A través de un sistema de retos diarios, puntos y un ranking competitivo, motivamos la participación constante. No es un juego infantil, sino un sistema de reconocimiento profesional que valora la colaboración.
Interacción sin Fricción: El principio fundamental es "mostrar y hablar, no escribir". La principal forma de contribución es a través de la grabación de pantalla y voz, una acción mucho más natural y rica en contexto que rellenar un documento.
Micro-tareas Guiadas: En lugar de enfrentarse a la tarea abrumadora de "documentar todo", el usuario recibe retos pequeños y atómicos ("Define la duración de este proceso", "Responde a esta pregunta concreta"). Esto reduce la carga cognitiva y hace que la colaboración sea manejable en pequeños huecos de tiempo.
Feedback y Refuerzo Positivo Constante: Cada acción completada es reconocida con puntos y animaciones de éxito. El sistema de "veredicto asíncrono" para las tareas complejas, seguido de una notificación de "puntos ganados", cierra el ciclo de feedback y genera satisfacción.
Un Propósito Definido en el Tiempo: La experiencia no es infinita. Está enmarcada en "Sprints" o "Fases de Colaboración" con una fecha de inicio y fin claras, visibles en todo momento a través de una barra de progreso. Esto crea un sentido de urgencia y un objetivo común.
B) Para el Gerente: Claridad y Control Estratégico
Visión 360º de un Vistazo: El Dashboard principal está diseñado para responder a la pregunta "¿Cómo está mi empresa hoy?" en menos de 30 segundos, a través de KPIs y gráficos visuales de alto impacto (procesos, hallazgos, etc.).
De lo General a lo Particular (Drill-down): El gerente puede pasar de una vista de alto nivel a un análisis profundo con un solo clic. Las pantallas de "Procesos" y "Hallazgos" son herramientas de exploración potentes, con filtros avanzados que le permiten "bucear" en los datos y entender las causas raíz.
Doble Rol Integrado: Reconocemos que el gerente también es un empleado. La interfaz integra de forma nativa su rol de líder (con acceso a los dashboards y datos de toda la empresa) y su rol de participante (con su propio "Reto del Día" y acceso a sus tareas personales).
Transparencia y Confianza: El portal es la prueba tangible y en tiempo real del trabajo que Aceleralia está realizando, moviendo la relación de una consultoría tradicional a una de socio tecnológico transparente.
5. Pilares Arquitectónicos y Tecnológicos
La visión se sustenta sobre una arquitectura moderna, robusta y escalable, basada en las mejores prácticas de la industria:
Stack Tecnológico Unificado: Utilizaremos React para el frontend y NestJS (sobre Node.js) para el backend. Esta elección nos permite trabajar con un único lenguaje (TypeScript/JavaScript) en todo el proyecto, garantizando la coherencia y la eficiencia del desarrollo.
Base de Datos Centralizada y Segura: Una única base de datos Supabase (PostgreSQL) servirá tanto para el portal como para nuestra aplicación interna, creando una única fuente de verdad. La seguridad se garantizará mediante un sistema de Row Level Security (RLS) basado en roles, asegurando que cada usuario solo acceda a la información que le corresponde.
Arquitectura de Servicios Desacoplada: El backend se construirá con servicios centralizados y reutilizables (StorageService, N8N_WebhookService, GamificationService), y el frontend con una biblioteca de componentes compartidos (RetoCard, KPI_Card), aplicando el principio DRY (Don't Repeat Yourself).
Orquestación Inteligente con N8N: Las tareas pesadas y asíncronas (análisis de vídeo/audio con IA, validaciones complejas, notificaciones) se delegarán a workflows de N8N, manteniendo nuestro backend principal ágil y enfocado en las interacciones en tiempo real.
Almacenamiento Escalable: Utilizaremos un flujo de almacenamiento robusto donde los archivos se suben a un "buzón" temporal en Supabase Storage para una respuesta rápida al usuario, y luego se mueven de forma asíncrona a Google Cloud Storage para su almacenamiento permanente y escalable.
