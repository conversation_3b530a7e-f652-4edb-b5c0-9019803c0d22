import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../common/database.service';

@Injectable()
export class GamificationService {
  constructor(private readonly databaseService: DatabaseService) {}

  async addPoints(userId: string, points: number): Promise<void> {
    const { data: user, error: getError } = await this.databaseService
      .getClient()
      .from('usuarios')
      .select('puntos')
      .eq('id', userId)
      .single<{ puntos: number }>();

    if (getError) {
      throw new Error(getError.message);
    }

    const newPoints = user.puntos + points;

    const { error: updateError } = await this.databaseService
      .getClient()
      .from('usuarios')
      .update({ puntos: newPoints })
      .eq('id', userId);

    if (updateError) {
      throw new Error(updateError.message);
    }
  }

  async completeSubtask(userId: string, subtaskId: string) {
    const { error } = await this.databaseService
      .getClient()
      .from('retos_subtareas')
      .update({ estado: 'completado' })
      .eq('id', subtaskId);

    if (error) {
      throw new Error(error.message);
    }

    await this.updateStreak(userId);
  }

  async updateStreak(userId: string): Promise<void> {
    const { data: user, error } = await this.databaseService
      .getClient()
      .from('usuarios')
      .select('racha_actual, ultima_actividad_racha')
      .eq('id', userId)
      .single<{ racha_actual: number; ultima_actividad_racha: string }>();

    if (error) {
      throw new Error(error.message);
    }

    const today = new Date().toISOString().slice(0, 10);
    const lastActivityDate = user.ultima_actividad_racha;

    if (lastActivityDate === today) {
      return;
    }

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = yesterday.toISOString().slice(0, 10);

    const newStreak =
      lastActivityDate === yesterdayString ? user.racha_actual + 1 : 1;

    const { error: updateError } = await this.databaseService
      .getClient()
      .from('usuarios')
      .update({ racha_actual: newStreak, ultima_actividad_racha: today })
      .eq('id', userId);

    if (updateError) {
      throw new Error(updateError.message);
    }
  }
}
