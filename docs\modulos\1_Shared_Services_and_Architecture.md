# Feature: 5. Shared Services & Architecture

## 1. Overview

This document outlines the foundational architecture and the set of common, reusable components and services that will be used across the entire application. The goal is to enforce the DRY (Don't Repeat Yourself) principle, ensure consistency, accelerate development, and simplify maintenance. This is the technical blueprint for building a robust and scalable application.

## 2. Requirements

*   **Requirement 1 (Centralized Backend Services):** Backend logic must be encapsulated in modular, injectable NestJS services with single responsibilities.
*   **Requirement 2 (Reusable Frontend Components):** The frontend must be built from a library of generic, reusable React components.
*   **Requirement 3 (Consistent Data Access):** All direct database interactions must be funneled through a single, dedicated `DatabaseService` to abstract the connection logic.
*   **Requirement 4 (Abstracted External Services):** All interactions with external services (Supabase, n8n, GCS) must be wrapped in dedicated services to decouple our application from specific third-party implementations.
*   **Requirement 5 (Type Safety):** The entire codebase, both frontend and backend, must be strictly typed using TypeScript to ensure data consistency and reduce runtime errors.

## 3. UI/UX Design

This module does not have a direct UI. Instead, it defines the building blocks used to create all user-facing interfaces.

*   **Key Reusable Components:**
    *   `<RetoCard />`: The primary component for displaying a challenge. It will have props to handle different states (pending, completed, expanded, collapsed).
    *   `<KPI_Card />`: A simple, visual card for displaying a key performance indicator.
    *   `<ProgressBar />`: A generic progress bar component.
    *   `<RankingList />`: Renders a list of users for the ranking, with logic to highlight the current user and the top 3.
    *   `<DataGrid />`: A powerful, configurable table component with built-in sorting and filtering capabilities. This is a major component.
    *   `<FileUpload />`: A component that encapsulates the logic for file selection, progress display, and the upload API call.
    *   `<Modal />`: A generic modal/dialog component.
    *   `<ToastNotification />`: A component for displaying pop-up notifications.

## 4. Technical Details

*   **Frontend Architecture:**
    *   **Framework:** React (with Vite for bundling).
    *   **State Management:** TanStack Query for server state, Zustand for global client state.
    *   **Styling:** A component library like Material-UI or a utility-first CSS framework like Tailwind CSS will be used for consistency.
    *   **Directory Structure:**
        ```
        /src
        |-- /components  # Reusable, generic components
        |-- /features    # Feature-specific components and views
        |-- /hooks       # Custom React hooks
        |-- /lib         # API client, utility functions
        |-- /store       # Zustand store definitions
        ```
*   **Backend Architecture (NestJS):**
    *   **Key Services:**
        *   `DatabaseService`: The only service that directly communicates with Supabase. It provides generic methods like `find`, `create`, `update` and handles the Supabase client instance with proper error handling.
        *   `AuthService`: Manages all authentication logic with JWT guards and input validation.
        *   `GamificationService`: Contains business logic for points, streaks, and rankings with comprehensive JSDoc documentation.
        *   `StorageService`: Wraps all interactions with Supabase Storage (temporary) and Google Cloud Storage (permanent) with file validation (MIME type, size limits).
        *   `N8N_WebhookService`: A simple service to trigger n8n workflows via HTTP POST requests with proper error handling and retry mechanisms.
    *   **Security Features:**
        *   Environment-based CORS configuration for flexible deployment
        *   Input validation with DTOs and class-validator
        *   File upload security with MIME type and size validation
        *   JWT authentication guards on all protected endpoints
        *   Comprehensive error handling without sensitive information exposure
    *   **API Documentation:**
        *   Swagger/OpenAPI integration available at `/api` endpoint
        *   Comprehensive API documentation with request/response schemas
        *   Bearer token authentication configuration for testing
    *   **Directory Structure:**
        ```
        /src
        |-- /auth        # Auth module (controller, service)
        |-- /database    # Database module and service
        |-- /gamification# Gamification module
        |-- /manager     # Manager-specific module
        |-- /shared      # Shared services (Storage, N8N, etc.)
        |-- /user        # User-related module
        ```
*   **Data Flow Example (Video Upload):**
    1.  **Frontend:** `<FileUpload>` component calls an API client function.
    2.  **API Client:** Sends a `POST` request to `/grabaciones`.
    3.  **Backend Controller (`GrabacionesController`):** Receives the request and calls `GrabacionesService`.
    4.  **Backend Service (`GrabacionesService`):**
        a. Calls `StorageService.uploadToTemporal(file)`.
        b. Calls `DatabaseService.create('grabaciones', ...)` to create the record.
        c. Calls `N8N_WebhookService.triggerWorkflow('procesarVideo', ...)`
    5.  **Response:** The backend immediately returns a success response to the frontend.

## 5. Acceptance Criteria

*   Given a developer needs to query the database, they must use the `DatabaseService` instead of accessing the Supabase client directly.
*   Given a developer needs to display a challenge, they must use the shared `<RetoCard />` component.
*   Given a developer needs to trigger an n8n workflow, they must use the `N8N_WebhookService`.
*   All new features must be built by composing existing shared components and services wherever possible.

## 6. Notes & Decisions

*   This service-oriented architecture is crucial for maintainability. If we ever need to swap out a third-party service (e.g., move from n8n to another orchestrator), we only need to update the corresponding service (`N8N_WebhookService`), and the rest of the application remains unchanged.
*   A shared TypeScript types/interfaces package will be created and used by both the frontend and backend to ensure data models are always in sync.