import { useState } from 'react';
import FileUpload from './FileUpload';
import InformationSourcesForm from './InformationSourcesForm';
import { Button } from '@/components/ui/button';
import { useSubmitGrabacion } from '../hooks/useSubmitGrabacion';
import { useAuthStore } from '@/features/auth/store/useAuthStore';
import { Loader2 } from 'lucide-react';
import { useGetGrabacionesByProceso } from '../hooks/useGetGrabacionesByProceso';
import { ChallengeStatusBadge } from './ChallengeStatusBadge';

interface Props {
  procesoId: string;
  onUploadComplete?: () => void;
}

const DefinirProcesoPanel = ({ procesoId, onUploadComplete }: Props) => {
  const [step, setStep] = useState(1);
  const user = useAuthStore((state) => state.user);
  const { mutate: submitGrabacion, isPending } = useSubmitGrabacion();
  const { data: grabaciones, isLoading: isLoadingGrabaciones } = useGetGrabacionesByProceso(procesoId);

  const handleUploadComplete = (files: File[]) => {
    if (!user) {
      console.error('User not authenticated');
      return;
    }
    const file = files[0];
    submitGrabacion(
      {
        file,
        entidad_relacionada_id: procesoId,
        entidad_relacionada_tipo: 'proceso_cliente',
        usuario_id: user.id,
        tipo_grabacion: 'video_pantalla',
      },
      {
        onSuccess: () => {
          setStep(2);
          if (onUploadComplete) {
            onUploadComplete();
          }
        },
      },
    );
  };

  const handleBack = () => {
    setStep(1);
  };

  return (
    <div className="p-4 border rounded-lg shadow-sm bg-white max-w-2xl mx-auto">
      <h2 className="text-xl font-bold text-gray-800 mb-4">Definir Nuevo Proceso</h2>
      
      {/* Stepper UI */}
      <div className="flex items-center justify-center mb-6">
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-indigo-600 text-white' : 'bg-gray-300 text-gray-600'}`}>1</div>
          <p className={`ml-2 ${step >= 1 ? 'text-indigo-600' : 'text-gray-600'}`}>Grabar Proceso</p>
        </div>
        <div className={`flex-auto border-t-2 mx-4 ${step >= 2 ? 'border-indigo-600' : 'border-gray-300'}`}></div>
        <div className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-indigo-600 text-white' : 'bg-gray-300 text-gray-600'}`}>2</div>
          <p className={`ml-2 ${step >= 2 ? 'text-indigo-600' : 'text-gray-600'}`}>Fuentes de Información</p>
        </div>
      </div>

      {/* Step Content */}
      <div className="transition-all duration-300">
        {step === 1 && (
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">Paso 1: Graba tu pantalla</h3>
            <p className="text-sm text-gray-600 mb-4">
              Graba un video de tu pantalla mostrando el proceso que quieres definir. Asegúrate de explicar cada paso en voz alta.
            </p>
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-700 mb-2">Grabaciones Anteriores</h4>
              {isLoadingGrabaciones ? (
                <div className="flex items-center justify-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span>Cargando grabaciones...</span>
                </div>
              ) : (
                <ul className="space-y-2">
                  {grabaciones?.map((grabacion) => (
                    <li key={grabacion.id} className="flex items-center justify-between p-2 border rounded-md">
                      <span className="text-sm">{new Date(grabacion.created_at).toLocaleString()}</span>
                      <ChallengeStatusBadge
                        status={grabacion.estado_procesamiento}
                        errorMessage={grabacion.info_adicional}
                      />
                    </li>
                  ))}
                </ul>
              )}
            </div>
            <FileUpload
              onUpload={handleUploadComplete}
              options={{
                accept: { 'video/*': [] },
                maxSize: 1024 * 1024 * 500, // 500MB
              }}
              disabled={isPending}
            />
            {isPending && (
              <div className="flex items-center justify-center mt-4">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                <span>Subiendo grabación...</span>
              </div>
            )}
          </div>
      )}
      {step === 2 && (
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">Paso 2: Documenta las fuentes de información</h3>
            <p className="text-sm text-gray-600 mb-4">
              Añade todas las fuentes de información necesarias para completar este proceso.
            </p>
            <InformationSourcesForm procesoId={procesoId} />
            <Button onClick={handleBack} variant="outline" className="mt-4">
              Volver al Paso 1
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DefinirProcesoPanel;