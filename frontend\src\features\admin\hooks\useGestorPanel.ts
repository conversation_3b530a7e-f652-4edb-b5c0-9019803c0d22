import { useQuery } from '@tanstack/react-query';
import api from '../../../lib/api';

interface SprintData {
  id: string;
  titulo: string;
  fecha_inicio: string;
  fecha_fin: string;
  estado: string;
  dias_transcurridos: number;
  dias_totales: number;
  retos_completados: number;
  retos_pendientes: number;
}

interface UsuarioPersona {
  usuario_id: string;
  persona_id: string;
  nombre_completo: string;
  cargo: string;
  departamento: string;
  rol: string;
  puntos_totales: number;
  retos_completados: number;
  numero_retos_diario: number;
  procesos_asignados: number;
  hallazgos_reportados: number;
}

interface PersonaSinUsuario {
  id: string;
  nombre_completo: string;
  cargo: string;
  departamento: string;
}

interface GestorPanelResponse {
  empresa: {
    id: string;
    nombre: string;
    logo_url?: string;
    sector?: string;
  };
  sprint_activo?: SprintData;
  historial_sprints: SprintData[];
  usuarios_personas: UsuarioPersona[];
  personas_sin_usuario: PersonaSinUsuario[];
}

/**
 * Hook to fetch comprehensive panel data for a company
 */
export const useGestorPanel = (empresaId: string) => {
  return useQuery({
    queryKey: ['admin', 'gestor-panel', empresaId],
    queryFn: async (): Promise<GestorPanelResponse> => {
      const response = await api.get<GestorPanelResponse>(`/admin/gestor/${empresaId}`);
      return response.data;
    },
    enabled: !!empresaId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
