import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import FileUpload from './FileUpload';
import { useSubmitRespuesta } from '../hooks/useSubmitRespuesta';
import { useSubmitGrabacion } from '../hooks/useSubmitGrabacion';
import { useAuthStore } from '@/features/auth/store/useAuthStore';
import { Loader2 } from 'lucide-react';

type Tab = 'text' | 'audio';

interface Props {
  preguntaId: string;
}

const ResponderPreguntaPanel = ({ preguntaId }: Props) => {
  const [activeTab, setActiveTab] = useState<Tab>('text');
  const [writtenAnswer, setWrittenAnswer] = useState('');
  const user = useAuthStore((state) => state.user);

  const { mutate: submitRespuesta, isPending: isSubmittingText } =
    useSubmitRespuesta();
  const { mutate: submitGrabacion, isPending: isSubmittingAudio } =
    useSubmitGrabacion();

  const handleTextSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    submitRespuesta({
      pregunta_id: preguntaId,
      respuesta_texto: writtenAnswer,
    });
  };

  const handleAudioUpload = (files: File[]) => {
    if (!user) {
      console.error('User not authenticated');
      return;
    }
    const file = files[0];
    submitGrabacion({
      file,
      entidad_relacionada_id: preguntaId,
      entidad_relacionada_tipo: 'pregunta_cliente',
      usuario_id: user.id,
      tipo_grabacion: 'solo_audio',
    });
  };

  const renderContent = () => {
    if (activeTab === 'text') {
      return (
        <form onSubmit={handleTextSubmit} className="space-y-4">
          <textarea
            value={writtenAnswer}
            onChange={(e) => setWrittenAnswer(e.target.value)}
            className="w-full p-2 border rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            rows={6}
            placeholder="Escribe tu respuesta aquí..."
            required
          />
          <Button
            type="submit"
            className="w-full"
            disabled={isSubmittingText}
          >
            {isSubmittingText && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Enviar Respuesta
          </Button>
        </form>
      );
    }
    if (activeTab === 'audio') {
      return (
        <>
          <FileUpload
            onUpload={handleAudioUpload}
            options={{ accept: { 'audio/*': [] } }}
            disabled={isSubmittingAudio}
          />
          {isSubmittingAudio && (
            <div className="flex items-center justify-center mt-4">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              <span>Subiendo audio...</span>
            </div>
          )}
        </>
      );
    }
    return null;
  };

  return (
    <div className="p-4 border rounded-lg shadow-sm bg-white max-w-md mx-auto">
      <h3 className="text-lg font-medium text-gray-800 mb-4">Responder a la Pregunta</h3>
      <div className="flex border-b mb-4">
        <button
          onClick={() => setActiveTab('text')}
          className={`px-4 py-2 -mb-px font-medium text-sm border-b-2 ${
            activeTab === 'text'
              ? 'border-indigo-500 text-indigo-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Respuesta Escrita
        </button>
        <button
          onClick={() => setActiveTab('audio')}
          className={`px-4 py-2 -mb-px font-medium text-sm border-b-2 ${
            activeTab === 'audio'
              ? 'border-indigo-500 text-indigo-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Grabar Audio
        </button>
      </div>
      <div>{renderContent()}</div>
    </div>
  );
};

export default ResponderPreguntaPanel;