import { ApiProperty } from '@nestjs/swagger';

export class KpiDataDto {
  @ApiProperty({ description: 'KPI title' })
  title: string;

  @ApiProperty({ description: 'KPI value' })
  value: number;

  @ApiProperty({ description: 'KPI change percentage', required: false })
  change?: number;

  @ApiProperty({
    description: 'KPI trend direction',
    enum: ['up', 'down', 'stable'],
    required: false,
  })
  trend?: 'up' | 'down' | 'stable';
}

export class ChartDataPointDto {
  @ApiProperty({ description: 'Data point label' })
  label: string;

  @ApiProperty({ description: 'Data point value' })
  value: number;

  @ApiProperty({ description: 'Data point color', required: false })
  color?: string;
}

export class DashboardSummaryDto {
  @ApiProperty({
    description: 'Key performance indicators',
    type: [KpiDataDto],
  })
  kpis: KpiDataDto[];

  @ApiProperty({
    description: 'Processes by department chart data',
    type: [ChartDataPointDto],
  })
  processesByDepartment: ChartDataPointDto[];

  @ApiProperty({
    description: 'Findings by type chart data',
    type: [ChartDataPointDto],
  })
  findingsByType: ChartDataPointDto[];

  @ApiProperty({
    description: 'Recent activity data',
    type: [ChartDataPointDto],
  })
  recentActivity: ChartDataPointDto[];

  @ApiProperty({
    description: 'Top performers ranking',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        nombre: { type: 'string' },
        puntos: { type: 'number' },
        departamento: { type: 'string' },
      },
    },
  })
  topPerformers: {
    id: string;
    nombre: string;
    puntos: number;
    departamento?: string;
  }[];

  @ApiProperty({
    description: 'Manager personal challenge',
    required: false,
    type: Object,
  })
  managerChallenge?: {
    id: string;
    titulo: string;
    descripcion: string;
    puntos_recompensa: number;
    estado: string;
  };
}
