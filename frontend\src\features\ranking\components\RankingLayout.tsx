import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { type User as Usuarios } from '@aceleralia/types';

const getRanking = async (): Promise<Usuarios[]> => {
  const response = await apiClient.get('/ranking');
  return response.data;
};

export const RankingLayout = () => {
  const { data: ranking, isLoading, isError } = useQuery({
    queryKey: ['ranking'],
    queryFn: getRanking,
  });

  return (
    <div className="p-4 md:p-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Ranking de la Empresa</h1>
        <p className="text-gray-500">
          ¡Mira quién lidera la tabla de clasificación!
        </p>
      </header>

      {isLoading && <LoadingSpinner />}
      {isError && <p>Error al cargar el ranking.</p>}

      {ranking && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Posición
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Puntos
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {ranking.map((user, index) => (
                <tr key={user.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.nombre} {user.apellidos}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.puntos}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};