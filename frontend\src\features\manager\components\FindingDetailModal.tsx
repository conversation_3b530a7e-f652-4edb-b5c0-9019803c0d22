import React from 'react';
import { useFindingDetail } from '../hooks/useFindings';
import Modal from '@/components/ui/Modal';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface FindingDetailModalProps {
  findingId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'ineficiencia': 'bg-red-100 text-red-800 border-red-200',
    'oportunidad_mejora': 'bg-green-100 text-green-800 border-green-200',
    'riesgo_identificado': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'ladron_tiempo': 'bg-orange-100 text-orange-800 border-orange-200',
    'falta_estandarizacion': 'bg-purple-100 text-purple-800 border-purple-200',
    'equipamiento_inadecuado': 'bg-blue-100 text-blue-800 border-blue-200',
    'deficit_gobernanza_datos': 'bg-indigo-100 text-indigo-800 border-indigo-200',
  };
  return colors[type] || 'bg-gray-100 text-gray-800 border-gray-200';
};

const getPriorityColor = (priority?: string) => {
  const colors: Record<string, string> = {
    'Urgente': 'bg-red-100 text-red-800 border-red-200',
    'Alta': 'bg-orange-100 text-orange-800 border-orange-200',
    'Media': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'Baja': 'bg-green-100 text-green-800 border-green-200',
  };
  return priority ? colors[priority] || 'bg-gray-100 text-gray-800 border-gray-200' : 'bg-gray-100 text-gray-800 border-gray-200';
};

const getImpactColor = (impact?: string) => {
  const colors: Record<string, string> = {
    'Alto': 'bg-red-100 text-red-800 border-red-200',
    'Medio': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'Bajo': 'bg-green-100 text-green-800 border-green-200',
  };
  return impact ? colors[impact] || 'bg-gray-100 text-gray-800 border-gray-200' : 'bg-gray-100 text-gray-800 border-gray-200';
};

export const FindingDetailModal: React.FC<FindingDetailModalProps> = ({
  findingId,
  isOpen,
  onClose
}) => {
  const { data: finding, isLoading, isError } = useFindingDetail(findingId || '');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'N/A';
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}min`;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Detalles del Hallazgo">
      <div className="max-w-4xl max-h-[80vh] overflow-y-auto">
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <LoadingSpinner />
          </div>
        )}

        {isError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-red-800 font-medium">Error al cargar el hallazgo</h3>
            <p className="text-red-600 text-sm mt-1">
              No se pudo cargar la información del hallazgo.
            </p>
          </div>
        )}

        {finding && (
          <div className="space-y-6">
            {/* Header */}
            <div className="border-b border-gray-200 pb-4">
              <h2 className="text-2xl font-bold text-gray-900 mb-3">
                {finding.titulo}
              </h2>
              <div className="flex flex-wrap items-center gap-3">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getTypeColor(finding.tipo)}`}>
                  {finding.tipo.replace(/_/g, ' ').toUpperCase()}
                </span>
                {finding.prioridad && (
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPriorityColor(finding.prioridad)}`}>
                    Prioridad: {finding.prioridad}
                  </span>
                )}
                {finding.nivel_impacto && (
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getImpactColor(finding.nivel_impacto)}`}>
                    Impacto: {finding.nivel_impacto}
                  </span>
                )}
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Información General
                </h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Departamento:</span>
                    <p className="text-gray-900">{finding.departamento}</p>
                  </div>
                  {finding.proceso_relacionado && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Proceso Relacionado:</span>
                      <p className="text-blue-600">{finding.proceso_relacionado.nombre_proceso}</p>
                    </div>
                  )}
                  {finding.estado && (
                    <div>
                      <span className="text-sm font-medium text-gray-500">Estado:</span>
                      <p className="text-gray-900 capitalize">{finding.estado}</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Identificado por
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900">
                    {finding.identificado_por.nombre} {finding.identificado_por.apellidos}
                  </h4>
                  <p className="text-gray-600 text-sm">{finding.identificado_por.cargo}</p>
                  <p className="text-gray-500 text-sm">{finding.identificado_por.departamento}</p>
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Descripción
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div>
                  <strong>Resumen:</strong>
                  <p className="text-gray-700 mt-1">{finding.descripcion}</p>
                </div>
                {finding.descripcion_detallada && (
                  <div>
                    <strong>Descripción detallada:</strong>
                    <div className="mt-2 whitespace-pre-wrap text-gray-700">
                      {finding.descripcion_detallada}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Impact and ROI Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {finding.tiempo_ahorro_estimado && (
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="font-semibold text-green-900">Ahorro de Tiempo Estimado</h4>
                  <p className="text-green-700">
                    {formatDuration(finding.tiempo_ahorro_estimado)}
                  </p>
                </div>
              )}
              {finding.esfuerzo_implementacion && (
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900">Esfuerzo de Implementación</h4>
                  <p className="text-blue-700 capitalize">
                    {finding.esfuerzo_implementacion}
                  </p>
                </div>
              )}
              {finding.roi_potencial && (
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="font-semibold text-purple-900">ROI Potencial</h4>
                  <p className="text-purple-700">
                    {finding.roi_potencial}
                  </p>
                </div>
              )}
            </div>

            {/* Recommended Actions */}
            {finding.acciones_recomendadas && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Acciones Recomendadas
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="whitespace-pre-wrap text-blue-900">
                    {finding.acciones_recomendadas}
                  </div>
                </div>
              </div>
            )}

            {/* Additional Information */}
            {finding.info_adicional && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Información Adicional
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="whitespace-pre-wrap text-gray-700">
                    {finding.info_adicional}
                  </div>
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="border-t border-gray-200 pt-4 text-sm text-gray-500">
              <div className="flex justify-between">
                <span>Creado: {formatDate(finding.created_at)}</span>
                <span>Actualizado: {formatDate(finding.updated_at)}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};
