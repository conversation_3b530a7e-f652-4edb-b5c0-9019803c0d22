import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../common/database.service';

@Injectable()
export class RetosService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findAll(userId: string, estado?: string) {
    const query = this.databaseService
      .getClient()
      .from('retos_usuarios')
      .select(
        `
        id,
        titulo,
        descripcion,
        puntos_recompensa,
        estado,
        prioridad,
        url_destino,
        retos_subtareas (
          id,
          titulo,
          descripcion,
          tipo_accion,
          estado
        )
      `,
      )
      .eq('usuario_id', userId);

    if (estado) {
      query.eq('estado', estado);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
}
