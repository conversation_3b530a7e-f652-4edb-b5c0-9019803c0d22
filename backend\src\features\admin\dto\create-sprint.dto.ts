import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsDateString, IsUUID } from 'class-validator';

export class CreateSprintDto {
  @ApiProperty({
    description: 'Sprint title',
    example: 'Fase 1: Mapeo de Procesos',
  })
  @IsString()
  titulo: string;

  @ApiProperty({
    description: 'Sprint start date',
    example: '2024-01-15',
  })
  @IsDateString()
  fecha_inicio: string;

  @ApiProperty({
    description: 'Sprint end date',
    example: '2024-02-15',
  })
  @IsDateString()
  fecha_fin: string;

  @ApiProperty({
    description: 'Company ID for the sprint',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsUUID()
  empresa_id: string;
}

export class CreateSprintResponseDto {
  @ApiProperty({
    description: 'Created sprint ID',
    example: 'd4e5f6a7-b8c9-0123-4567-890abcdef123',
  })
  id: string;

  @ApiProperty({
    description: 'Sprint title',
    example: 'Fase 1: Mapeo de Procesos',
  })
  titulo: string;

  @ApiProperty({
    description: 'Sprint start date',
    example: '2024-01-15',
  })
  fecha_inicio: string;

  @ApiProperty({
    description: 'Sprint end date',
    example: '2024-02-15',
  })
  fecha_fin: string;

  @ApiProperty({
    description: 'Sprint status',
    example: 'planificada',
  })
  estado: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Sprint creado exitosamente',
  })
  message: string;
}
