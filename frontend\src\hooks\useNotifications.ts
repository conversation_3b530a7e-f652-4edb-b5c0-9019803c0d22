import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { useSessionStore } from '../store/session';
import { useToastStore } from '../store/toast';
import type { RealtimePostgresInsertPayload } from '@supabase/supabase-js';

interface Notification {
  id: string;
  usuario_id: string;
  titulo: string;
  mensaje: string;
  url_destino: string;
  estado: string;
  tipo_notificacion: string;
  created_at: string;
}

export const useNotifications = () => {
  const queryClient = useQueryClient();
  const user = useSessionStore((state) => state.user);
  const showToast = useToastStore((state) => state.showToast);

  useEffect(() => {
    if (!user) return;

    const handleNewNotification = (
      payload: RealtimePostgresInsertPayload<Notification>,
    ) => {
      showToast(payload.new.mensaje, 'info');
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    };

    const channel = supabase
      .channel('notificaciones')
      .on<Notification>(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notificaciones',
          filter: `usuario_id=eq.${user.id}`,
        },
        handleNewNotification,
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, queryClient, showToast]);
};