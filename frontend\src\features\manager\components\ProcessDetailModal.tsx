import React from 'react';
import { useProcessDetail } from '../hooks/useProcesses';
import Modal from '@/components/ui/Modal';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ProcessDetailModalProps {
  processId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

export const ProcessDetailModal: React.FC<ProcessDetailModalProps> = ({
  processId,
  isOpen,
  onClose
}) => {
  const { data: process, isLoading, isError } = useProcessDetail(processId || '');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'N/A';
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}min`;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Detalles del Proceso">
      <div className="max-w-4xl max-h-[80vh] overflow-y-auto">
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <LoadingSpinner />
          </div>
        )}

        {isError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-red-800 font-medium">Error al cargar el proceso</h3>
            <p className="text-red-600 text-sm mt-1">
              No se pudo cargar la información del proceso.
            </p>
          </div>
        )}

        {process && (
          <div className="space-y-6">
            {/* Header */}
            <div className="border-b border-gray-200 pb-4">
              <h2 className="text-2xl font-bold text-gray-900">
                {process.nombre_proceso}
              </h2>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <span>Departamento: <strong>{process.departamento}</strong></span>
                <span>•</span>
                <span>Duración: <strong>{formatDuration(process.duracion_minutos)}</strong></span>
                <span>•</span>
                <span>Frecuencia: <strong>{process.frecuencia}</strong></span>
              </div>
            </div>

            {/* Description */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Descripción
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-700 mb-3">
                  <strong>Resumen:</strong> {process.descripcion_breve}
                </p>
                {process.descripcion_detallada && (
                  <div>
                    <strong>Descripción detallada:</strong>
                    <div className="mt-2 whitespace-pre-wrap text-gray-700">
                      {process.descripcion_detallada}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Process Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-semibold text-blue-900">Complejidad</h4>
                <p className="text-blue-700 capitalize">
                  {process.complejidad || 'No definida'}
                </p>
              </div>
              <div className="bg-green-50 rounded-lg p-4">
                <h4 className="font-semibold text-green-900">Potencial de Automatización</h4>
                <p className="text-green-700 capitalize">
                  {process.potencial_automatizacion || 'No evaluado'}
                </p>
              </div>
              <div className="bg-purple-50 rounded-lg p-4">
                <h4 className="font-semibold text-purple-900">Hallazgos Relacionados</h4>
                <p className="text-purple-700">
                  {process.total_hallazgos} hallazgos
                </p>
              </div>
            </div>

            {/* Tasks */}
            {process.tareas.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Tareas del Proceso ({process.tareas.length})
                </h3>
                <div className="space-y-3">
                  {process.tareas
                    .sort((a, b) => a.orden - b.orden)
                    .map((task) => (
                      <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                {task.orden}
                              </span>
                              <h4 className="font-medium text-gray-900">
                                {task.nombre_tarea}
                              </h4>
                            </div>
                            <p className="text-gray-600 text-sm mt-2">
                              {task.descripcion}
                            </p>
                            {task.responsable && (
                              <p className="text-gray-500 text-sm mt-1">
                                Responsable: {task.responsable}
                              </p>
                            )}
                          </div>
                          {task.duracion_minutos && (
                            <span className="text-sm text-gray-500 ml-4">
                              {formatDuration(task.duracion_minutos)}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Responsible People */}
            {process.responsables.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Personas Responsables ({process.responsables.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {process.responsables.map((person) => (
                    <div key={person.id} className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900">
                        {person.nombre} {person.apellidos}
                      </h4>
                      <p className="text-gray-600 text-sm">{person.cargo}</p>
                      <p className="text-gray-500 text-sm">{person.departamento}</p>
                      <p className="text-blue-600 text-sm mt-1">{person.email}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Information Sources */}
            {process.fuentes_informacion.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Fuentes de Información ({process.fuentes_informacion.length})
                </h3>
                <div className="space-y-3">
                  {process.fuentes_informacion.map((source) => (
                    <div key={source.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">
                            {source.nombre_informacion}
                          </h4>
                          {source.descripcion && (
                            <p className="text-gray-600 text-sm mt-1">
                              {source.descripcion}
                            </p>
                          )}
                          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                            <span>Formato: <strong>{source.formato}</strong></span>
                            <span>•</span>
                            <span>Responsable: <strong>{source.persona_responsable}</strong></span>
                          </div>
                        </div>
                        {source.url_adjunto && (
                          <a
                            href={source.url_adjunto}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm ml-4"
                          >
                            Ver archivo
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="border-t border-gray-200 pt-4 text-sm text-gray-500">
              <div className="flex justify-between">
                <span>Creado: {formatDate(process.created_at)}</span>
                <span>Actualizado: {formatDate(process.updated_at)}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};
