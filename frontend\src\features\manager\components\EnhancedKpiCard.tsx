import React from 'react';
import { type KpiData } from '@aceleralia/types';

interface EnhancedKpiCardProps extends KpiData {
  icon?: React.ReactNode;
}

const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
  switch (trend) {
    case 'up':
      return <span className="text-green-500">↗</span>;
    case 'down':
      return <span className="text-red-500">↘</span>;
    case 'stable':
      return <span className="text-gray-500">→</span>;
    default:
      return null;
  }
};

const getTrendColor = (trend?: 'up' | 'down' | 'stable') => {
  switch (trend) {
    case 'up':
      return 'text-green-600';
    case 'down':
      return 'text-red-600';
    case 'stable':
      return 'text-gray-600';
    default:
      return 'text-gray-600';
  }
};

export const EnhancedKpiCard: React.FC<EnhancedKpiCardProps> = ({ 
  title, 
  value, 
  change, 
  trend, 
  icon 
}) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
            {title}
          </h3>
          <div className="mt-2 flex items-baseline">
            <p className="text-3xl font-bold text-gray-900">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {change !== undefined && (
              <div className={`ml-2 flex items-center text-sm ${getTrendColor(trend)}`}>
                {getTrendIcon(trend)}
                <span className="ml-1">
                  {change > 0 ? '+' : ''}{change}%
                </span>
              </div>
            )}
          </div>
        </div>
        {icon && (
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600">
              {icon}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
