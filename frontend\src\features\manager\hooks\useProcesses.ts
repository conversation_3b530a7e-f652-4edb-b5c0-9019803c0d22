import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { type ProcessListResponse, type ProcessDetail } from '@aceleralia/types';

interface ProcessListParams {
  page?: number;
  limit?: number;
  departamento?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

const getProcesses = async (params: ProcessListParams): Promise<ProcessListResponse> => {
  const response = await apiClient.get('/manager/procesos', { params });
  return response.data;
};

const getProcessDetail = async (processId: string): Promise<ProcessDetail> => {
  const response = await apiClient.get(`/manager/procesos/${processId}`);
  return response.data;
};

export const useProcesses = (params: ProcessListParams) => {
  return useQuery({
    queryKey: ['managerProcesses', params],
    queryFn: () => getProcesses(params),
  });
};

export const useProcessDetail = (processId: string) => {
  return useQuery({
    queryKey: ['processDetail', processId],
    queryFn: () => getProcessDetail(processId),
    enabled: !!processId,
  });
};
