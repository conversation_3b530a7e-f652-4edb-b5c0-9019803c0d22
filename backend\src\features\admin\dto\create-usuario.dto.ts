import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength, IsEnum, IsUUID } from 'class-validator';

export class CreateUsuarioDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Initial password for the user',
    example: 'TempPassword123!',
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  password: string;

  @ApiProperty({
    description: 'User role',
    enum: ['cliente_gerente', 'cliente_empleado'],
    example: 'cliente_empleado',
  })
  @IsEnum(['cliente_gerente', 'cliente_empleado'])
  rol: 'cliente_gerente' | 'cliente_empleado';

  @ApiProperty({
    description: 'Company ID the user belongs to',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  @IsUUID()
  empresa_id: string;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  @IsString()
  nombre: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Pérez García',
  })
  @IsString()
  apellidos: string;
}

export class CreateUsuarioResponseDto {
  @ApiProperty({
    description: 'Created user ID',
    example: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
  })
  id: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User full name',
    example: 'Juan Pérez García',
  })
  nombre_completo: string;

  @ApiProperty({
    description: 'User role',
    example: 'cliente_empleado',
  })
  rol: string;

  @ApiProperty({
    description: 'Success message',
    example: 'Usuario creado exitosamente',
  })
  message: string;
}
