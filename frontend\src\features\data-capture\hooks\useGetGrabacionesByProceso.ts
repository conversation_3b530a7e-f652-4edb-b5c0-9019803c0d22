import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import type { Grabacion } from '@packages/types/src';

const getGrabacionesByProceso = async (procesoId: string): Promise<Grabacion[]> => {
  const { data, error } = await supabase
    .from('grabaciones')
    .select('*')
    .eq('entidad_relacionada_id', procesoId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(error.message);
  }

  return data || [];
};

export const useGetGrabacionesByProceso = (procesoId: string) => {
  return useQuery({
    queryKey: ['grabaciones', procesoId],
    queryFn: () => getGrabacionesByProceso(procesoId),
  });
};