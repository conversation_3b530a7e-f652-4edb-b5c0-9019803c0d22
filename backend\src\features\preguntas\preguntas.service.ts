import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from 'src/common/database.service';
import { TableTypes } from 'src/common/database.types';
import { CreateRespuestaDto } from './dto/create-respuesta.dto';

type Pregunta = TableTypes['preguntas']['select'];

@Injectable()
export class PreguntasService {
  constructor(private readonly databaseService: DatabaseService) {}

  async addRespuesta(
    createRespuestaDto: CreateRespuestaDto,
  ): Promise<Pregunta> {
    const { preguntaId, respuestaTexto } = createRespuestaDto;

    const response = await this.databaseService
      .getClient()
      .from('preguntas')
      .update({
        respuesta_texto: respuestaTexto,
        estado: 'respondida',
      })
      .eq('id', preguntaId)
      .select()
      .single<Pregunta>();

    const data = response.data;
    const error = response.error;

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundException(
          `Pregunta with ID ${preguntaId} not found.`,
        );
      }
      throw new InternalServerErrorException(
        'Failed to update pregunta.',
        error.message,
      );
    }

    if (!data) {
      throw new NotFoundException(`Pregunta with ID ${preguntaId} not found.`);
    }

    return data;
  }
}
