import React, { useState, useMemo } from 'react';
import { useFindings } from '../hooks/useFindings';
import { DataGrid } from '@/components/ui/DataGrid';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { type ColumnDef } from '@tanstack/react-table';
import { type FindingListItem } from '@aceleralia/types';

interface FindingsExplorerProps {
  onFindingSelect?: (findingId: string) => void;
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'ineficiencia': 'bg-red-100 text-red-800',
    'oportunidad_mejora': 'bg-green-100 text-green-800',
    'riesgo_identificado': 'bg-yellow-100 text-yellow-800',
    'ladron_tiempo': 'bg-orange-100 text-orange-800',
    'falta_estandarizacion': 'bg-purple-100 text-purple-800',
    'equipamiento_inadecuado': 'bg-blue-100 text-blue-800',
    'deficit_gobernanza_datos': 'bg-indigo-100 text-indigo-800',
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getPriorityColor = (priority?: string) => {
  const colors: Record<string, string> = {
    'Urgente': 'bg-red-100 text-red-800',
    'Alta': 'bg-orange-100 text-orange-800',
    'Media': 'bg-yellow-100 text-yellow-800',
    'Baja': 'bg-green-100 text-green-800',
  };
  return priority ? colors[priority] || 'bg-gray-100 text-gray-800' : 'bg-gray-100 text-gray-800';
};

export const FindingsExplorer: React.FC<FindingsExplorerProps> = ({ 
  onFindingSelect 
}) => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    tipo: '',
    departamento: '',
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc' as 'asc' | 'desc'
  });

  const { data: findingsData, isLoading, isError } = useFindings(filters);

  const columns: ColumnDef<FindingListItem>[] = useMemo(() => [
    {
      accessorKey: 'titulo',
      header: 'Hallazgo',
      cell: ({ row }) => (
        <div>
          <div className="font-medium text-gray-900">
            {row.original.titulo}
          </div>
          <div className="text-sm text-gray-500 truncate max-w-xs">
            {row.original.descripcion}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'tipo',
      header: 'Tipo',
      cell: ({ getValue }) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(getValue() as string)}`}>
          {(getValue() as string).replace(/_/g, ' ').toUpperCase()}
        </span>
      ),
    },
    {
      accessorKey: 'departamento',
      header: 'Departamento',
      cell: ({ getValue }) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: 'proceso_relacionado',
      header: 'Proceso',
      cell: ({ getValue }) => {
        const proceso = getValue() as string | undefined;
        return proceso ? (
          <span className="text-sm text-gray-600">{proceso}</span>
        ) : (
          <span className="text-sm text-gray-400">N/A</span>
        );
      },
    },
    {
      accessorKey: 'prioridad',
      header: 'Prioridad',
      cell: ({ getValue }) => {
        const priority = getValue() as string | undefined;
        return priority ? (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(priority)}`}>
            {priority}
          </span>
        ) : (
          <span className="text-sm text-gray-400">N/A</span>
        );
      },
    },
    {
      accessorKey: 'identificado_por',
      header: 'Identificado por',
      cell: ({ getValue }) => (
        <span className="text-sm text-gray-600">{getValue() as string}</span>
      ),
    },
    {
      id: 'actions',
      header: 'Acciones',
      cell: ({ row }) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onFindingSelect?.(row.original.id)}
        >
          Ver Detalles
        </Button>
      ),
    },
  ], [onFindingSelect]);

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : (typeof value === 'number' ? value : 1) // Reset to page 1 when filtering
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">Error al cargar hallazgos</h3>
        <p className="text-red-600 text-sm mt-1">
          No se pudieron cargar los hallazgos. Por favor, intenta de nuevo.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-800">
          Explorador de Hallazgos
        </h2>
        <p className="text-gray-600 text-sm mt-1">
          Explora y analiza todos los hallazgos identificados en tu empresa
        </p>
      </div>

      {/* Filters */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Buscar hallazgo
            </label>
            <Input
              type="text"
              placeholder="Título o descripción..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo de hallazgo
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.tipo}
              onChange={(e) => handleFilterChange('tipo', e.target.value)}
            >
              <option value="">Todos los tipos</option>
              <option value="ineficiencia">Ineficiencia</option>
              <option value="oportunidad_mejora">Oportunidad de Mejora</option>
              <option value="riesgo_identificado">Riesgo Identificado</option>
              <option value="ladron_tiempo">Ladrón de Tiempo</option>
              <option value="falta_estandarizacion">Falta de Estandarización</option>
              <option value="equipamiento_inadecuado">Equipamiento Inadecuado</option>
              <option value="deficit_gobernanza_datos">Déficit Gobernanza Datos</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Departamento
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.departamento}
              onChange={(e) => handleFilterChange('departamento', e.target.value)}
            >
              <option value="">Todos los departamentos</option>
              <option value="Comercial">Comercial</option>
              <option value="Operaciones">Operaciones</option>
              <option value="Recursos Humanos">Recursos Humanos</option>
              <option value="Finanzas">Finanzas</option>
              <option value="IT">IT</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ordenar por
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                setFilters(prev => ({ ...prev, sortBy, sortOrder: sortOrder as 'asc' | 'desc' }));
              }}
            >
              <option value="created_at-desc">Más recientes</option>
              <option value="created_at-asc">Más antiguos</option>
              <option value="titulo-asc">Título A-Z</option>
              <option value="titulo-desc">Título Z-A</option>
              <option value="tipo-asc">Tipo A-Z</option>
              <option value="prioridad-desc">Prioridad (Alta a Baja)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Data Grid */}
      <div className="p-6">
        {findingsData && findingsData.data.length > 0 ? (
          <>
            <DataGrid
              data={findingsData.data}
              columns={columns}
            />
            
            {/* Pagination */}
            <div className="mt-6 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Mostrando {((filters.page - 1) * filters.limit) + 1} a{' '}
                {Math.min(filters.page * filters.limit, findingsData.meta.total)} de{' '}
                {findingsData.meta.total} hallazgos
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(filters.page - 1)}
                  disabled={filters.page <= 1}
                >
                  Anterior
                </Button>
                
                <span className="text-sm text-gray-700">
                  Página {filters.page} de {findingsData.meta.totalPages}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(filters.page + 1)}
                  disabled={filters.page >= findingsData.meta.totalPages}
                >
                  Siguiente
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <p className="text-lg font-medium">No se encontraron hallazgos</p>
              <p className="text-sm mt-2">
                Intenta ajustar los filtros o verifica que existan hallazgos registrados.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
