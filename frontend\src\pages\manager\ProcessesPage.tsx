import React, { useState } from 'react';
import { ProcessesExplorer } from '@/features/manager/components/ProcessesExplorer';
import { ProcessDetailModal } from '@/features/manager/components/ProcessDetailModal';

const ProcessesPage: React.FC = () => {
  const [selectedProcessId, setSelectedProcessId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleProcessSelect = (processId: string) => {
    setSelectedProcessId(processId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedProcessId(null);
  };

  return (
    <div className="p-4 md:p-8 bg-gray-50 min-h-screen">
      <ProcessesExplorer onProcessSelect={handleProcessSelect} />
      
      <ProcessDetailModal
        processId={selectedProcessId}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default ProcessesPage;
