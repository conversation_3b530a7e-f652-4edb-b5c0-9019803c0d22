import React from 'react';
import { useLogin } from '../hooks/useAuth';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useToastStore } from '../../../store/toast';
import { LoadingSpinner } from '../../../components/ui/LoadingSpinner';
import { Link } from 'react-router-dom';

export const LoginForm: React.FC = () => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [showPassword, setShowPassword] = React.useState(false);
  const { showToast } = useToastStore();
  const loginMutation = useLogin();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      showToast('Por favor, completa todos los campos.', 'warning');
      return;
    }
    loginMutation.mutate({ email, password });
  };

  return (
    <div className="px-2">
      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="form-group">
          <label htmlFor="email" className="form-label flex items-center gap-2">
            <Mail className="h-4 w-4 text-primary-600" />
            Correo Electrónico
          </label>
          <div className="relative">
            <input
              id="email"
              className="input pl-4 h-12 text-base border-2 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200"
              type="email"
              name="email"
              placeholder="<EMAIL>"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loginMutation.isPending}
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="password" className="form-label flex items-center gap-2">
            <Lock className="h-4 w-4 text-primary-600" />
            Contraseña
          </label>
          <div className="relative">
            <input
              id="password"
              className="input pl-4 pr-12 h-12 text-base border-2 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200"
              type={showPassword ? 'text' : 'password'}
              name="password"
              placeholder="Tu contraseña"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loginMutation.isPending}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-4 flex items-center hover:bg-gray-50 rounded-r-md transition-colors duration-200"
              onClick={() => setShowPassword(!showPassword)}
              disabled={loginMutation.isPending}
              aria-label={showPassword ? 'Ocultar contraseña' : 'Mostrar contraseña'}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-gray-500 hover:text-gray-700" />
              ) : (
                <Eye className="h-5 w-5 text-gray-500 hover:text-gray-700" />
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center justify-end pt-2">
          <Link
            to="/forgot-password"
            className="text-sm text-primary-600 hover:text-primary-700 font-medium hover:underline transition-all duration-200"
          >
            ¿Olvidaste tu contraseña?
          </Link>
        </div>

        <div className="pt-2">
          <button
            type="submit"
            disabled={loginMutation.isPending}
            className="btn btn-primary w-full h-12 text-base font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:transform-none disabled:shadow-md"
          >
            {loginMutation.isPending && (
              <LoadingSpinner size="sm" color="white" className="mr-3" />
            )}
            {loginMutation.isPending ? 'Iniciando sesión...' : 'Iniciar Sesión'}
          </button>
        </div>

        <div className="pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-600 text-center leading-relaxed">
            Al iniciar sesión, aceptas nuestros{' '}
            <a href="#" className="text-primary-600 hover:text-primary-700 underline font-medium">
              Términos de Servicio
            </a>{' '}
            y{' '}
            <a href="#" className="text-primary-600 hover:text-primary-700 underline font-medium">
              Política de Privacidad
            </a>
          </p>
        </div>
      </form>
    </div>
  );
};