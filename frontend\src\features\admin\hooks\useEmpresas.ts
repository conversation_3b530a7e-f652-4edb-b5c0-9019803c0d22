import { useQuery } from '@tanstack/react-query';
import api from '../../../lib/api';

interface EmpresaListItem {
  id: string;
  nombre: string;
  logo_url?: string;
  sector?: string;
  total_usuarios: number;
  total_procesos: number;
}

interface EmpresaListResponse {
  empresas: EmpresaListItem[];
}

/**
 * Hook to fetch list of companies for admin panel
 */
export const useEmpresas = () => {
  return useQuery({
    queryKey: ['admin', 'empresas'],
    queryFn: async (): Promise<EmpresaListResponse> => {
      const response = await api.get<EmpresaListResponse>('/admin/empresas');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
