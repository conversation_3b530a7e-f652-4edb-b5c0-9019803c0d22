# n8n Workflows

This document provides a detailed description of the n8n workflows used in the application.

## 1. Process Recording Workflow

**Workflow Name:** Process Recording
**Workflow ID:** `processRecording`

### 1.1. Trigger

The workflow is triggered by a webhook call from the backend.

### 1.2. Webhook URL Configuration

The webhook URL for this workflow must be configured in the application's environment variables. The backend will retrieve the URL using the key `N8N_PROCESS_RECORDING_WEBHOOK_URL`.

To configure the webhook URL:
1.  Create a new workflow in n8n.
2.  Add a "Webhook" node as the trigger.
3.  Copy the "Test URL" from the Webhook node.
4.  Add the following line to your `.env` file:

```
N8N_PROCESS_RECORDING_WEBHOOK_URL=<YOUR_N8N_WEBHOOK_URL>
```

### 1.3. Input Payload

The workflow will receive a JSON payload with the following structure:

```json
{
  "version": "1.0",
  "eventType": "new_recording",
  "data": {
    "grabacionId": "string",
    "usuarioId": "string",
    "entidadRelacionadaId": "string",
    "entidadRelacionadaTipo": "proceso_cliente" | "pregunta_cliente" | "tarea_cliente"
  }
}
```

### 1.4. Core Logic

The workflow must perform the following steps:

1.  **Fetch Recording Record:**
    *   Use the `grabacionId` from the input payload to fetch the corresponding record from the `grabaciones` table in the database.

2.  **Download Media File:**
    *   Use the `url_almacenamiento` from the `grabaciones` record to download the media file from the temporary storage (Supabase Storage).

3.  **Upload to Permanent Storage:**
    *   Upload the media file to the permanent storage solution (Google Cloud Storage).

4.  **Update Recording URL:**
    *   Update the `url_almacenamiento` in the `grabaciones` table with the new URL from the permanent storage.

5.  **AI Analysis:**
    *   Send the media file to the designated AI service for transcription and analysis.
    *   The prompt should instruct the AI to extract a detailed process description.

6.  **Parse AI Response:**
    *   Parse the JSON response from the AI service.
    *   Extract the generated `descripcion_detallada` for the process.

7.  **Update Business Tables:**
    *   Based on the `entidad_relacionada_tipo`, update the corresponding business table:
        *   If `entidad_relacionada_tipo` is `proceso_cliente`:
            *   Update the `procesos_clientes` table with the new `descripcion_detallada`.
            *   Change the `estado_analisis` to `pendiente_revision_humana`.
        *   If `entidad_relacionada_tipo` is `pregunta_cliente`:
            *   Update the `respuestas_pregunta` table with the AI-generated answer.
        *   If `entidad_relacionada_tipo` is `tarea_cliente`:
            *   Update the `tareas_clientes` table with the relevant information from the AI analysis.

8.  **Update Subtask Status:**
    *   After successfully updating the business table, check if the second step (information sources) has also been completed for the process.
    *   Only set the `retos_subtareas` record's `estado` to `completado` if both the recording has been processed AND the information sources have been submitted.

9.  **Update Transcription:**
    *   Populate the `transcripcion` field in the `grabaciones` record with the text from the AI service.

10. **Cleanup:**
    *   Delete the file from the temporary storage (Supabase Storage).

### 1.5. Expected Output

Upon successful completion, the workflow should have:
*   Moved the media file to permanent storage.
*   Updated the `grabaciones` table with the new URL and transcription.
*   Updated the relevant business table with the AI-generated content.
*   Marked the corresponding `retos_subtareas` record as `completado`.
*   Deleted the file from temporary storage.