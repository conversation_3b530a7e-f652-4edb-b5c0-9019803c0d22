import React, { useState, useMemo } from 'react';
import { useProcesses } from '../hooks/useProcesses';
import { DataGrid } from '@/components/ui/DataGrid';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { type ColumnDef } from '@tanstack/react-table';
import { type ProcessListItem } from '@aceleralia/types';

interface ProcessesExplorerProps {
  onProcessSelect?: (processId: string) => void;
}

export const ProcessesExplorer: React.FC<ProcessesExplorerProps> = ({ 
  onProcessSelect 
}) => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    departamento: '',
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc' as 'asc' | 'desc'
  });

  const { data: processesData, isLoading, isError } = useProcesses(filters);

  const columns: ColumnDef<ProcessListItem>[] = useMemo(() => [
    {
      accessorKey: 'nombre_proceso',
      header: 'Proceso',
      cell: ({ row }) => (
        <div>
          <div className="font-medium text-gray-900">
            {row.original.nombre_proceso}
          </div>
          <div className="text-sm text-gray-500 truncate max-w-xs">
            {row.original.descripcion_breve}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'departamento',
      header: 'Departamento',
      cell: ({ getValue }) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: 'responsable',
      header: 'Responsable',
    },
    {
      accessorKey: 'duracion_minutos',
      header: 'Duración',
      cell: ({ getValue }) => {
        const duration = getValue() as number | undefined;
        return duration ? `${duration} min` : 'N/A';
      },
    },
    {
      accessorKey: 'frecuencia',
      header: 'Frecuencia',
    },
    {
      accessorKey: 'total_tareas',
      header: 'Tareas',
      cell: ({ getValue }) => (
        <span className="text-center block">{getValue() as number}</span>
      ),
    },
    {
      accessorKey: 'total_hallazgos',
      header: 'Hallazgos',
      cell: ({ getValue }) => (
        <span className="text-center block">{getValue() as number}</span>
      ),
    },
    {
      id: 'actions',
      header: 'Acciones',
      cell: ({ row }) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onProcessSelect?.(row.original.id)}
        >
          Ver Detalles
        </Button>
      ),
    },
  ], [onProcessSelect]);

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : (typeof value === 'number' ? value : 1) // Reset to page 1 when filtering
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">Error al cargar procesos</h3>
        <p className="text-red-600 text-sm mt-1">
          No se pudieron cargar los procesos. Por favor, intenta de nuevo.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-800">
          Explorador de Procesos
        </h2>
        <p className="text-gray-600 text-sm mt-1">
          Explora y analiza todos los procesos identificados en tu empresa
        </p>
      </div>

      {/* Filters */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Buscar proceso
            </label>
            <Input
              type="text"
              placeholder="Nombre del proceso..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Departamento
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.departamento}
              onChange={(e) => handleFilterChange('departamento', e.target.value)}
            >
              <option value="">Todos los departamentos</option>
              <option value="Comercial">Comercial</option>
              <option value="Operaciones">Operaciones</option>
              <option value="Recursos Humanos">Recursos Humanos</option>
              <option value="Finanzas">Finanzas</option>
              <option value="IT">IT</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ordenar por
            </label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                setFilters(prev => ({ ...prev, sortBy, sortOrder: sortOrder as 'asc' | 'desc' }));
              }}
            >
              <option value="created_at-desc">Más recientes</option>
              <option value="created_at-asc">Más antiguos</option>
              <option value="nombre_proceso-asc">Nombre A-Z</option>
              <option value="nombre_proceso-desc">Nombre Z-A</option>
              <option value="departamento-asc">Departamento A-Z</option>
            </select>
          </div>
        </div>
      </div>

      {/* Data Grid */}
      <div className="p-6">
        {processesData && processesData.data.length > 0 ? (
          <>
            <DataGrid
              data={processesData.data}
              columns={columns}
            />
            
            {/* Pagination */}
            <div className="mt-6 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Mostrando {((filters.page - 1) * filters.limit) + 1} a{' '}
                {Math.min(filters.page * filters.limit, processesData.meta.total)} de{' '}
                {processesData.meta.total} procesos
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(filters.page - 1)}
                  disabled={filters.page <= 1}
                >
                  Anterior
                </Button>
                
                <span className="text-sm text-gray-700">
                  Página {filters.page} de {processesData.meta.totalPages}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(filters.page + 1)}
                  disabled={filters.page >= processesData.meta.totalPages}
                >
                  Siguiente
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <p className="text-lg font-medium">No se encontraron procesos</p>
              <p className="text-sm mt-2">
                Intenta ajustar los filtros o verifica que existan procesos registrados.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
