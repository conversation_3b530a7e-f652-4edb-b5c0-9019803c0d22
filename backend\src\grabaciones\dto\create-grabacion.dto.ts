import { IsString, IsNotEmpty, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateGrabacionDto {
  @ApiProperty({
    description: 'ID of the related entity',
    example: 'uuid-string',
  })
  @IsString()
  @IsNotEmpty()
  entidad_relacionada_id: string;

  @ApiProperty({
    description: 'Type of the related entity',
    enum: ['proceso_cliente', 'hallazgo_cliente', 'reto_subtarea'],
    example: 'reto_subtarea',
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['proceso_cliente', 'hallazgo_cliente', 'reto_subtarea'])
  entidad_relacionada_tipo: string;

  @ApiProperty({
    description: 'ID of the user uploading the file',
    example: 'uuid-string',
  })
  @IsString()
  @IsNotEmpty()
  usuario_id: string;

  @ApiProperty({
    description: 'Type of recording',
    enum: ['video', 'audio', 'pantalla'],
    example: 'video',
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['video', 'audio', 'pantalla'])
  tipo_grabacion: string;
}
