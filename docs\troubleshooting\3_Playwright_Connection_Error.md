# Troubleshooting: Playwright `net::ERR_CONNECTION_REFUSED`

## 1. Symptom

When running Playwright tests that attempt to navigate to the Vite development server (e.g., `http://localhost:5173`), the test fails immediately with a `net::ERR_CONNECTION_REFUSED` error.

## 2. Context

This issue was encountered during **Sprint 3: Frontend Authentication Flow** on `2025-07-10`. The Vite development server was running correctly and was accessible from a standard web browser, but the Playwright-controlled browser instance could not establish a connection.

## 3. Possible Causes & Solutions

This error typically indicates that the browser instance launched by <PERSON><PERSON> cannot reach the specified address.

*   **Firewall or Security Software:** A local firewall (like Windows Defender Firewall) or other security software might be blocking the connection from the Playwright browser process.
    *   **Solution:** Temporarily disable the firewall or add a specific rule to allow inbound connections on port 5173 for Node.js or the specific browser executable used by <PERSON>wright.

*   **Vite Server Host Binding:** By default, Vite may bind to `localhost`, which can sometimes be ambiguous. Binding explicitly to `127.0.0.1` can resolve this.
    *   **Solution:** Modify the `dev` script in `frontend/package.json` to include the `--host` flag:
        ```json
        "scripts": {
          "dev": "vite --host 127.0.0.1"
        }
        ```

*   **Playwright Configuration:** There might be a proxy or network setting within the Playwright configuration that is interfering with the connection.
    *   **Solution:** Review the `playwright.config.ts` (if it exists) for any `proxy` settings or other network-related configurations that could be misdirecting traffic.

*   **Race Condition:** The test might be running before the Vite server is fully ready to accept connections, even if the terminal shows it has started.
    *   **Solution:** Introduce a small delay before the first navigation or use Vite's programmatic API to start the server and wait for it to be ready before running tests.

## 4. Immediate Workaround

As a temporary workaround to continue development, manual testing was performed in a standard browser. The issue was documented, and the resolution is deferred to avoid blocking sprint completion.

## 5. Status

*   **Resolved:** No
*   **Next Steps:** Investigate the potential causes listed above, starting with the Vite server host binding and firewall rules.