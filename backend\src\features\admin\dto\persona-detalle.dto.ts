import { ApiProperty } from '@nestjs/swagger';

export class PersonaKpiDto {
  @ApiProperty({
    description: 'Assigned processes count',
    example: 3,
  })
  procesos_asignados: number;

  @ApiProperty({
    description: 'Reported findings count',
    example: 5,
  })
  hallazgos_reportados: number;

  @ApiProperty({
    description: 'Completed challenges count',
    example: 12,
  })
  retos_completados: number;

  @ApiProperty({
    description: 'Total accumulated points',
    example: 450,
  })
  puntos_acumulados: number;
}

export class ProcesoPersonaDto {
  @ApiProperty({
    description: 'Process ID',
    example: 'e5f6a7b8-c9d0-1234-5678-90abcdef1234',
  })
  id: string;

  @ApiProperty({
    description: 'Process name',
    example: 'Gestión de Pedidos',
  })
  nombre: string;

  @ApiProperty({
    description: 'Process description',
    example:
      'Proceso completo de gestión de pedidos desde recepción hasta entrega',
  })
  descripcion: string;

  @ApiProperty({
    description: 'Analysis status',
    example: 'definicion_completa',
  })
  estado_analisis: string;

  @ApiProperty({
    description: 'Duration in minutes per execution',
    example: 45,
  })
  duracion_minutos_por_ejecucion: number;

  @ApiProperty({
    description: 'Is repetitive process',
    example: true,
  })
  es_repetitivo: boolean;
}

export class HallazgoPersonaDto {
  @ApiProperty({
    description: 'Finding ID',
    example: 'f6a7b8c9-d0e1-2345-6789-0abcdef12345',
  })
  id: string;

  @ApiProperty({
    description: 'Finding title',
    example: 'Duplicación de datos en sistema de inventario',
  })
  titulo: string;

  @ApiProperty({
    description: 'Finding type',
    example: 'ineficiencia',
  })
  tipo: string;

  @ApiProperty({
    description: 'Finding description',
    example:
      'Se detecta duplicación de registros en el sistema de inventario causando inconsistencias',
  })
  descripcion: string;

  @ApiProperty({
    description: 'Impact description',
    example: 'Genera confusión en el equipo y errores en reportes',
  })
  impacto: string;

  @ApiProperty({
    description: 'Finding status',
    example: 'identificado',
  })
  estado: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-15T10:30:00Z',
  })
  created_at: string;
}

export class PersonaDetalleResponseDto {
  @ApiProperty({
    description: 'Person basic information',
    type: 'object',
    properties: {
      id: { type: 'string' },
      nombre_completo: { type: 'string' },
      cargo: { type: 'string' },
      departamento: { type: 'string' },
      email: { type: 'string' },
      telefono: { type: 'string' },
    },
  })
  persona: {
    id: string;
    nombre_completo: string;
    cargo: string;
    departamento: string;
    email?: string;
    telefono?: string;
  };

  @ApiProperty({
    description: 'User information if linked',
    type: 'object',
    properties: {
      id: { type: 'string' },
      email: { type: 'string' },
      rol: { type: 'string' },
      puntos: { type: 'number' },
      racha_actual: { type: 'number' },
      ultimo_acceso: { type: 'string' },
    },
  })
  usuario?: {
    id: string;
    email: string;
    rol: string;
    puntos: number;
    racha_actual: number;
    ultimo_acceso?: string;
  };

  @ApiProperty({
    description: 'Person KPIs',
    type: PersonaKpiDto,
  })
  kpis: PersonaKpiDto;

  @ApiProperty({
    description: 'Processes assigned to this person',
    type: [ProcesoPersonaDto],
  })
  procesos: ProcesoPersonaDto[];

  @ApiProperty({
    description: 'Findings reported by this person',
    type: [HallazgoPersonaDto],
  })
  hallazgos: HallazgoPersonaDto[];
}
