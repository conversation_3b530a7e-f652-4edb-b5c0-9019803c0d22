import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../../../lib/api';
import { useToastStore } from '../../../store/toast';

interface CreateSprintRequest {
  titulo: string;
  fecha_inicio: string;
  fecha_fin: string;
  empresa_id: string;
}

interface CreateSprintResponse {
  id: string;
  titulo: string;
  fecha_inicio: string;
  fecha_fin: string;
  estado: string;
  message: string;
}

/**
 * Hook to create a new sprint
 */
export const useCreateSprint = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: async (data: CreateSprintRequest): Promise<CreateSprintResponse> => {
      const response = await api.post<CreateSprintResponse>('/admin/sprints', data);
      return response.data;
    },
    onSuccess: (data) => {
      showToast(data.message, 'success');
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['admin', 'gestor-panel'] });
    },
    onError: (error: any) => {
      showToast(error.response?.data?.message || 'Error al crear sprint', 'error');
    },
  });
};
