import { Module } from '@nestjs/common';
import { ProcesosFuentesInformacionService } from './procesos-fuentes-informacion.service';
import { ProcesosFuentesInformacionController } from './procesos-fuentes-informacion.controller';
import { SharedModule } from 'src/shared/shared.module';

@Module({
  imports: [SharedModule],
  controllers: [ProcesosFuentesInformacionController],
  providers: [ProcesosFuentesInformacionService],
})
export class ProcesosFuentesInformacionModule {}
