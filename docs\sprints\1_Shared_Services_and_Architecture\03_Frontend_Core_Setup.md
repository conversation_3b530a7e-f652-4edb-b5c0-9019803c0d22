# Sprint Log: 1.3 Frontend Core Setup - YYYY-MM-DD

## 1. Sprint Goal(s)

*   Goal 1: Initialize the React (Vite) frontend project with all necessary dependencies.
*   Goal 2: Establish the core application structure, including directories for components, features, hooks, and libraries.
*   Goal 3: Implement the global state management solution (Zustand) and the server state management solution (TanStack Query).
*   Goal 4: Create a shared, type-safe API client for communicating with the backend.

## 1.b Relevant Feature Documents

*   `docs/modulos/1_Shared_Services_and_Architecture.md`
*   `docs/ARCHITECTURE.md`

## 2. Planned Tasks

*   [ ] Initialize a new React project using Vite.
*   [ ] Install and configure all base dependencies (e.g., `react-router-dom`, `axios`, `zustand`, `@tanstack/react-query`).
*   [ ] Define the main directory structure (`/components`, `/features`, `/hooks`, etc.).
*   [ ] Set up a global Zustand store for managing user session and UI state.
*   [ ] Configure a global TanStack Query client.
*   [ ] Create a type-safe API client (e.g., using Axios) with base URL and interceptors for handling JWTs.
*   [ ] Create a shared types/interfaces package to be used by both frontend and backend.