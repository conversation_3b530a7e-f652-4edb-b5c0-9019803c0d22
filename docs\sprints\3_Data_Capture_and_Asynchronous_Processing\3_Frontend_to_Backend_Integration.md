# Sprint 16: Frontend-to-Backend Integration

## 1. Sprint Goal
The goal of this sprint is to connect the frontend data capture components (from Sprint 15) with the backend API endpoints (from Sprint 14). This will create the initial end-to-end data flow, allowing a user to submit data through the UI and have it successfully stored in the database. **COMPLETED**

## 2. Key Tasks
- **Integrate `<DefinirProcesoPanel />`:**
    - Use TanStack Query's `useMutation` hook to handle the `POST /grabaciones` request when a user submits a file.
    - Provide clear UI feedback during the upload process (loading indicators) and upon completion (toast notifications).
- **Integrate `<ResponderPreguntaPanel />`:**
    - Wire the text area to the `POST /respuestas-pregunta` endpoint.
    - Wire the audio recorder to the `POST /grabaciones` endpoint, ensuring the correct `entidad_relacionada` data is sent.
- **Integrate `<DefinirDuracionForm />`:**
    - Connect the form's submission to the `PATCH /procesos-clientes/:id/duracion` endpoint.
- **Integrate `<InformationSourcesForm />`:**
    - Connect the form's submission to the new `POST /procesos-fuentes-informacion` endpoint.
    - Handle fetching the list of `personas` for the dropdown.
- **State Management:**
    - Ensure the UI correctly reflects the state of the submission (loading, success, error).
    - Use Zustand or component state as appropriate to manage the UI state.
- **API Client Updates:**
    - Add the new API functions to the type-safe API client (`frontend/src/lib/api.ts`).

## 3. Acceptance Criteria
- Submitting a file through the `<DefinirProcesoPanel />` results in a new `grabaciones` record in the database with `estado_procesamiento: 'pendiente'`.
- Submitting a written answer through `<ResponderPreguntaPanel />` updates the corresponding `preguntas` record.
- Submitting an audio answer through `<ResponderPreguntaPanel />` creates a new `grabaciones` record linked to the correct question.
- Submitting the `<DefinirDuracionForm />` updates the correct `procesos_clientes` record.
- The user receives immediate and clear feedback in the UI after each submission attempt (success or error).

## 4. Key Files to Be Created/Modified
- `frontend/src/features/data-capture/components/DefinirProcesoPanel.tsx` (Modified)
- `frontend/src/features/data-capture/components/ResponderPreguntaPanel.tsx` (Modified)
- `frontend/src/features/data-capture/components/DefinirDuracionForm.tsx` (Modified)
- `frontend/src/lib/api.ts` (Modified)
- `frontend/src/features/data-capture/hooks/useSubmitGrabacion.ts` (New or similar)
- `frontend/src/features/data-capture/hooks/useSubmitRespuesta.ts` (New or similar)