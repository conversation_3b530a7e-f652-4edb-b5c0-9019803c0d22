import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { type HomeDashboard } from '@aceleralia/types';

const getDashboardHome = async (): Promise<HomeDashboard> => {
  const response = await apiClient.get('/dashboard/home');
  return response.data;
};

export const useGetDashboard = () => {
  return useQuery({
    queryKey: ['dashboardHome'],
    queryFn: getDashboardHome,
  });
};