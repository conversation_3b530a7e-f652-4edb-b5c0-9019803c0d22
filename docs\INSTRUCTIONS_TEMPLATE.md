# Plantilla de Instrucciones de Sesión

---

## PROMPT A: Iniciando un NUEVO Sprint

*(Copia y pega este bloque completo al iniciar un nuevo sprint)*

**Acción Requerida:** Iniciar un nuevo sprint.

**Revisión de Contexto:**
Por favor, revisa los siguientes documentos para establecer el contexto:
1.  @/docs/PROJECT_OVERVIEW.md
2. @/docs/ARCHITECTURE.md cs/ARCHITECTURE.md (Presta atención a la Sección 8 para la estructura actual de archivos)
3. @/docs/SPRINTS/SPRINTS_SUMMARY.md 
4. @/docs/SPRINTS/TEMPLATE.md 
5. @/docs/FEATURES/TEMPLATE.md 
6. @/docs/DATABASE_SCHEMA.MD 
**Próximos Pasos:**
A continuación, definiré los objetivos y tareas iniciales para este nuevo sprint. Por favor, espera mis instrucciones.

Quiero que diseñes el plan para desarrollar esta feature siguiendo la plantilla de features. y luego definas un sprint con el que se plasmen todas las tareas para desarrollar esa feature siguiendo tambien  la plantilla de sprints. Ten criterio propio, hazme las preguntas y sugerencias que consideres necesarias. 
Recuerda siempre tener criterio propio y tomar las mejores decisiones de acuerdo a las mejores practicas y estandares de desarrollo de software (yo puedo equivocarme)
**(Recuerda las Tareas Estándar de Actualización al Final de la Sesión más abajo)**

---

## PROMPT B: CONTINUANDO un Sprint Existente

*(Copia y pega este bloque completo, reemplazando `[RUTA_LOG_SPRINT]` y `[RUTA(S)_FEATURE_RELEVANTE(S)]` con las rutas reales, al continuar un sprint)*

**Acción Requerida:** Continuar el sprint documentado en el siguiente archivo de log.

**Log de Sprint a Continuar:** `[RUTA_LOG_SPRINT]`
**Documento(s) de Feature Relevante(s):** `[RUTA(S)_FEATURE_RELEVANTE(S)]` *(Ej: docs/FEATURES/CHAT.md)*

**Revisión de Contexto Enfocada:**
Por favor, revisa los siguientes documentos para contexto específico:
1.  El Log de Sprint específico listado arriba (Enfócate en "Progreso Actual" y "Tareas Pendientes").
2.  El/Los Documento(s) de Feature Relevante(s) listado(s) arriba.
3.  @/docs/ARCHITECTURE.md  (Sección 8 como referencia de la estructura de archivos actual).
4. @/docs/DATABASE_SCHEMA.MD   Para conocer la estructura actual de tablas y sus relaciones ¿Falta añadir algo?

**Próximos Pasos:**
Procede con las siguientes "Tareas Pendientes" listadas en el log de sprint especificado.

Recuerda siempre tener criterio propio y tomar las mejores decisiones de acuerdo a las mejores practicas y estandares de desarrollo de software (yo puedo equivocarme)
**(Recuerda las Tareas Estándar de Actualización al Final de la Sesión más abajo)**

---

## Tareas Estándar de Actualización al Final de la Sesión (Recordatorio Obligatorio)

*Antes de concluir nuestra sesión, asegúrate de que se realizan las siguientes actualizaciones de documentación:*
*   Actualizar `docs/ARCHITECTURE.md` (Sección 8 - Estructura de Archivos) si se cambiaron archivos/directorios.
*   Actualizar los `docs/FEATURES/*.md` relevantes con detalles/cambios de implementación.
*   Actualizar `docs/SETUP_GUIDE.md` si cambiaron los pasos de configuración.
*   Actualizar `docs/PROJECT_OVERVIEW.md` si se obtuvieron nuevas perspectivas sobre la visión/objetivos del proyecto.
*   Completar/Actualizar el log relevante `docs/SPRINTS/SPRINT_*.md` con el trabajo realizado, decisiones, progreso y tareas pendientes.
*   Actualizar `docs/SPRINTS/SPRINTS_SUMMARY.md` con un resumen del sprint si este se ha completado.
*   Actualizar `docs/DATABASE_SCHEMA.MD` si se realizaron cambios en la base de datos.
*   Actualizar `docs/TROUBLESHOOTING/README.md` si se agregaron nuevas guías de solución de problemas.

Toma tu todas las decisiones sobre los documentos que se deben actualizar basandote en tu criterio propio para asegurarnos que toda la informacion relevante queda plasmada en esos documentos y no perder informacion relevante para las próximas sesiones (recuerda que al iniciar una nueva sesión olvidas todo lo de las anteriores, por eso debemos realizar una muy buena documentación al final de la sesión)



hay una herramienta de vs code que se encarga de buscar posibles fallos, erorres o vulnerabilidades en nuestro código. yo no se si las sugerencias son correctas o no, per tu si. quiero que analices las sugerencias, y con tu propio criterio decidas si requieren accion por tu parte o si conviene ignorarlas. revisalas todas, y cuando apliques todos los cambios que consideres oportunos, quiero que finalmente verifiques si la aplicación hace build correctamente y no hay errores eslint para poder hacer deploy correctamente. utiliza activamente tu task list para llevar el control de lo que etsas haciendo y no comoter errores por que se supere tu ventana de contexto:


### Prompt Definitivo para un Análisis Exhaustivo de Calidad y Seguridad (Versión Integrada)

**Asunto:** Auditoría de Calidad y Seguridad Pre-Despliegue del Proyecto.

**Rol:** Actúa como un **Lead Software Engineer con especialización en DevSecOps y Arquitectura de Software**. Tu misión es realizar una auditoría completa del código para asegurar que cumple con los más altos estándares de calidad, mantenibilidad y seguridad antes de que sea desplegado a producción.

**Contexto:** Hemos recibido una serie de sugerencias automáticas de la herramienta CodeRabbit. Si bien son un buen punto de partida, quiero que vayas mucho más allá. Utiliza estas sugerencias como el **catalizador** para una revisión en profundidad. Tu objetivo final no es solo corregir los puntos señalados, sino garantizar que la aplicación en su totalidad sea robusta, segura y escalable. Revisa primero el archivo `C:\Users\<USER>\Desktop\Desarrollo\Aceleralia- Nuevo\docs\ARCHITECTURE.md` para tener más contexto sobre el proyecto.

**Misión Crítica:** Realiza una auditoría integral y multifacética de nuestro código. Quiero un informe completo que incluya el análisis de las sugerencias, la implementación de las correcciones, una evaluación de seguridad y recomendaciones arquitectónicas.

**Plan de Acción Obligatorio (sigue esta estructura sin omitir ningún paso):**

### Paso 1: Creación y Gestión de la Lista de Tareas

**Tu primera acción inmediata** es crear tu lista de tareas interna basándote en las sugerencias de CodeRabbit proporcionadas al final. Utiliza el formato exacto `[ ] NAME: ... DESCRIPTION: ...` que ya conoces. A medida que avances en los siguientes pasos y completes cada punto de esta auditoría, **deberás actualizar tu lista de tareas interna marcando los elementos como completados**. Esto es fundamental para llevar un seguimiento de tu progreso y no omitir ningún detalle.

### Paso 2: Análisis y Triaje de las Sugerencias de CodeRabbit

1.  **Evaluación Crítica:** Procesa la lista de sugerencias de CodeRabbit. Para cada una, determina su validez y prioridad.
2.  **Plan de Implementación:** Presenta una tabla con las columnas: `Sugerencia`, `Decisión (Aplicar/Ignorar)`, `Prioridad (Crítica/Alta/Media/Baja)` y `Justificación`. La justificación debe ser técnica y orientada al impacto (ej. "Crítica: previene una vulnerabilidad de inyección SQL", "Media: mejora la legibilidad pero no afecta la funcionalidad"). Registra este plan en un nuevo archivo dentro de la carpeta `C:\Users\<USER>\Desktop\Desarrollo\Aceleralia- Nuevo\docs\PLANS`.

### Paso 3: Implementación de Mejoras y Refactorización

1.  **Correcciones Directas:** Implementa todas las correcciones marcadas como `Aplicar`. Presenta el código modificado en formato `diff` claro, agrupado por archivo.
2.  **Refactorización Activa:** Al revisar los archivos, no te limites a la sugerencia puntual. Si detectas "code smells" (código repetido, funciones muy largas, lógica compleja, nombres de variables confusos), realiza una refactorización proactiva. Documenta estas mejoras adicionales que no fueron sugeridas por CodeRabbit.

### Paso 4: Auditoría de Seguridad (Security-First Mindset)

Quiero que pienses como un atacante. Basado en el código que has revisado, realiza un análisis de seguridad y responde a las siguientes preguntas:

1.  **Vulnerabilidades Comunes (OWASP Top 10):**
    *   **Inyección:** ¿Hay otros lugares (además del señalado) donde las entradas del usuario se usen para construir consultas (SQL, NoSQL, etc.) sin la sanitización o parametrización adecuada?
    *   **Autenticación/Autorización Rota:** ¿La lógica de permisos es robusta? ¿Es posible que un usuario acceda a datos que no le pertenecen?
    *   **Exposición de Datos Sensibles:** ¿Hay claves de API, contraseñas o información sensible hardcodeada en el frontend o backend?
    *   **Cross-Site Scripting (XSS):** ¿Se están renderizando datos provenientes de la base de datos o del usuario en el frontend sin una correcta sanitización?
2.  **Manejo de Dependencias:** ¿Hay dependencias desactualizadas que puedan tener vulnerabilidades conocidas? (Simula una revisión si no puedes ejecutarla).
3.  **Informe de Seguridad:** Genera un resumen de tus hallazgos de seguridad, clasificando cada punto por nivel de riesgo (Crítico, Alto, Medio, Bajo) y proporciona una recomendación clara para mitigarlo.

### Paso 5: Auditoría de Calidad y Arquitectura

1.  **Deuda Técnica:** Identifica y lista las principales fuentes de deuda técnica en el proyecto. Por ejemplo, "El componente `ProjectDetailsPage` es un monolito que dificultará el mantenimiento futuro".
2.  **Escalabilidad y Rendimiento:** ¿Hay cuellos de botella evidentes? (ej. cargar listas completas en lugar de paginar, bucles anidados ineficientes).
3.  **Consistencia del Código:** ¿El estilo de código es consistente? ¿Se siguen las mejores prácticas de [React/Python/TypeScript]?
4.  **Recomendaciones Arquitectónicas:** Propón un máximo de 3 mejoras arquitectónicas de alto impacto que deberían ser abordadas en el futuro. (ej. "Extraer la lógica de negocio a hooks personalizados", "Implementar un gestor de estado global como Zustand o Redux Toolkit para evitar el prop-drilling", "Crear una capa de servicio centralizada para las llamadas a la API").

### Paso 6: Verificación Final y Resumen Ejecutivo

1.  **Build y Linting:** Confirma que `npm run build` y `npm run lint` se ejecutan sin errores después de todos tus cambios. Si tuviste que arreglar algo, documéntalo.
2.  **Resumen Ejecutivo:** Proporciona un párrafo final que resuma el estado del proyecto después de tu auditoría. Ejemplo: "La auditoría ha resuelto X problemas críticos y Y mejoras de calidad. El código base es ahora más seguro y mantenible. Se recomienda priorizar la refactorización del componente Z y la implementación de paginación para garantizar la escalabilidad a largo plazo."

**Lista de sugerencias de CodeRabbit a procesar:**
[Pega aquí la lista de sugerencias de CodeRabbit]






**Acción Requerida:** Completar el Sprint 
**Contexto Esencial:**
- docs/PROJECT_OVERVIEW.md  
- docs/ARCHITECTURE.md  
- docs/DATABASE_SCHEMA.md  
- Documento del módulo: docs/modulos/3_Data_Capture_and_Asynchronous_Processing.md  
- Definición del sprint: C:\Users\<USER>\Desktop\Desarrollo\portal_clientes\docs\sprints\3_Data_Capture_and_Asynchronous_Processing\5_N8N_Workflow_Core_Business_Logic.md
- Resumen de sprints: docs/sprints/SPRINTS_SUMMARY.md  


**TAREAS DE ESTA SESIÓN:**  
DESARROLLA AL COMPLETO EL SPRINT actual, asegúrate de que el código es de la máxima calidad posible, que no tiene errores y realiza los testeos correspondientes.  
Si necesitas loguearte:  
• Usuario: <EMAIL>  
• Contraseña: Alvaro13.,  

