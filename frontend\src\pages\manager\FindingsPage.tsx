import React, { useState } from 'react';
import { FindingsExplorer } from '@/features/manager/components/FindingsExplorer';
import { FindingDetailModal } from '@/features/manager/components/FindingDetailModal';

const FindingsPage: React.FC = () => {
  const [selectedFindingId, setSelectedFindingId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleFindingSelect = (findingId: string) => {
    setSelectedFindingId(findingId);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedFindingId(null);
  };

  return (
    <div className="p-4 md:p-8 bg-gray-50 min-h-screen">
      <FindingsExplorer onFindingSelect={handleFindingSelect} />
      
      <FindingDetailModal
        findingId={selectedFindingId}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default FindingsPage;
