import React from 'react';

interface UserRanking {
  id: string;
  name: string;
  points: number;
  avatarUrl?: string;
}

interface RankingListProps {
  users: UserRanking[];
  currentUserId: string;
}

const getRankColor = (rank: number) => {
  switch (rank) {
    case 0:
      return 'bg-yellow-300 text-yellow-800'; // Gold
    case 1:
      return 'bg-gray-300 text-gray-800'; // Silver
    case 2:
      return 'bg-yellow-600 bg-opacity-50 text-yellow-900'; // Bronze
    default:
      return 'bg-gray-100 text-gray-700';
  }
};

export const RankingList: React.FC<RankingListProps> = ({ users, currentUserId }) => {
  // Sort users by points in descending order
  const sortedUsers = [...users].sort((a, b) => b.points - a.points);

  return (
    <div className="bg-white shadow-lg rounded-lg p-4 w-full max-w-md">
      <h2 className="text-xl font-bold text-center text-gray-800 mb-4">Ranking de Puntuación</h2>
      <ul className="space-y-2">
        {sortedUsers.map((user, index) => {
          const isCurrentUser = user.id === currentUserId;
          const rankColor = getRankColor(index);

          return (
            <li
              key={user.id}
              className={`flex items-center p-3 rounded-lg transition-all duration-200 ${
                isCurrentUser ? 'ring-2 ring-blue-500 scale-105' : ''
              } ${rankColor}`}
            >
              <span className={`font-bold w-8 text-center text-lg`}>{index + 1}</span>
              <img
                src={user.avatarUrl || `https://i.pravatar.cc/40?u=${user.id}`}
                alt={user.name}
                className="w-10 h-10 rounded-full mx-4 border-2 border-white"
              />
              <span className="flex-grow font-medium">{user.name}</span>
              <span className="font-bold text-lg">{user.points} pts</span>
            </li>
          );
        })}
      </ul>
    </div>
  );
};