import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useDefinirDuracion } from '../hooks/useDefinirDuracion';
import { Loader2 } from 'lucide-react';

interface Props {
  procesoId: string;
}

const DefinirDuracionForm = ({ procesoId }: Props) => {
  const [duration, setDuration] = useState('');
  const [period, setPeriod] = useState('minutes');
  const { mutate: definirDuracion, isPending } = useDefinirDuracion();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    definirDuracion({
      proceso_id: procesoId,
      duracion: parseInt(duration, 10),
      periodo: period,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-4 border rounded-lg shadow-sm bg-white">
      <h3 className="text-lg font-medium text-gray-800">Definir Duración del Proceso</h3>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="duration">Duración</Label>
          <Input
            id="duration"
            type="number"
            value={duration}
            onChange={(e) => setDuration(e.target.value)}
            placeholder="Ej: 30"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="period">Periodo</Label>
          <select
            id="period"
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="minutes">Minutos</option>
            <option value="hours">Horas</option>
            <option value="days">Días</option>
          </select>
        </div>
      </div>
      <Button type="submit" className="w-full" disabled={isPending}>
        {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        Guardar Duración
      </Button>
    </form>
  );
};

export default DefinirDuracionForm;