import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { TableTypes } from './database.types';

export interface Database {
  public: {
    Tables: TableTypes;
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
  };
}

@Injectable()
export class DatabaseService implements OnModuleInit {
  private supabase: SupabaseClient<Database>;
  private supabaseServiceRole: SupabaseClient<Database>;

  constructor(private readonly configService: ConfigService) {}

  onModuleInit() {
    const supabaseUrl = this.configService.get<string>('SUPABASE_URL');
    const supabaseAnonKey = this.configService.get<string>('SUPABASE_ANON_KEY');
    const supabaseServiceKey = this.configService.get<string>('SUPABASE_SERVICE_KEY');

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase URL and Anon Key must be provided.');
    }

    // Regular client with RLS
    this.supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

    // Service role client that bypasses RLS
    if (supabaseServiceKey) {
      this.supabaseServiceRole = createClient<Database>(supabaseUrl, supabaseServiceKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });
      console.log('Supabase service role client initialized.');
    }

    console.log('Supabase client initialized.');
  }

  getClient(): SupabaseClient<Database> {
    return this.supabase;
  }

  /**
   * Get service role client that bypasses RLS
   * Use only for admin operations
   */
  getServiceClient(): SupabaseClient<Database> {
    if (!this.supabaseServiceRole) {
      throw new Error('Service role client not initialized. Check SUPABASE_SERVICE_KEY configuration.');
    }
    return this.supabaseServiceRole;
  }
}
