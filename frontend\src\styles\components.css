/* Custom component styles using Tailwind utilities */

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 6px 20px 0 rgba(37, 99, 235, 0.35);
  transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  transform: translateY(0);
}

.btn-secondary {
  background-color: #f1f5f9;
  color: #0f172a;
}

.btn-secondary:hover {
  background-color: #e2e8f0;
}

.btn-secondary:active {
  background-color: #cbd5e1;
}

.btn-outline {
  border: 1px solid #93c5fd;
  color: #1d4ed8;
  background-color: transparent;
}

.btn-outline:hover {
  background-color: #eff6ff;
}

.btn-outline:active {
  background-color: #dbeafe;
}

.btn-ghost {
  color: #334155;
  background-color: transparent;
}

.btn-ghost:hover {
  background-color: #f1f5f9;
}

.btn-ghost:active {
  background-color: #e2e8f0;
}

/* Button Sizes */
.btn-sm {
  height: 2rem;
  padding: 0 0.75rem;
  font-size: 0.75rem;
}

.btn-md {
  height: 2.5rem;
  padding: 0.5rem 1rem;
}

.btn-lg {
  height: 3rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Input Components */
.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.input::placeholder {
  color: #9ca3af;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.input:hover:not(:focus):not(:disabled) {
  border-color: #9ca3af;
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background-color: #f9fafb;
}

/* Card Components */
.card {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  padding-bottom: 1rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1;
  letter-spacing: -0.025em;
  color: #0f172a;
}

.card-description {
  font-size: 0.875rem;
  color: #475569;
}

.card-content {
  padding-top: 0;
}

.card-footer {
  display: flex;
  align-items: center;
  padding-top: 1rem;
}

/* Form Components */
.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  letter-spacing: 0.025em;
}

.form-error {
  font-size: 0.75rem;
  color: #dc2626;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Focus and interaction improvements */
.input:focus + .form-label,
.input:not(:placeholder-shown) + .form-label {
  color: #2563eb;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Toast/Alert Components */
.toast {
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.toast-success {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.toast-error {
  background-color: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.toast-warning {
  background-color: #fffbeb;
  color: #92400e;
  border: 1px solid #fed7aa;
}

.toast-info {
  background-color: #eff6ff;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

/* Loading Spinner */
.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Responsive design improvements */
@media (max-width: 640px) {
  .card {
    padding: 1.5rem;
    margin: 1rem;
  }

  .btn-lg {
    height: 3rem;
    font-size: 1rem;
  }

  .input {
    height: 3rem;
    font-size: 1rem;
  }
}

/* Utility Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  100% { 
    transform: translateY(0); 
    opacity: 1; 
  }
}
