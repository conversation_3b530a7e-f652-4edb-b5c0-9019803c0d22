import {
  ChallengeCard,
  RankingList,
  FileUpload,
  DataGrid,
} from '../../components/ui';
import type { ColumnDef } from '@tanstack/react-table';

// Mock Data
const mockUsers = [
  { id: '1', name: '<PERSON>', points: 1250, avatarUrl: 'https://i.pravatar.cc/40?u=1' },
  { id: '2', name: '<PERSON>', points: 1100, avatarUrl: 'https://i.pravatar.cc/40?u=2' },
  { id: '3', name: '<PERSON>', points: 1300, avatarUrl: 'https://i.pravatar.cc/40?u=3' },
  { id: '4', name: '<PERSON><PERSON>', points: 950, avatarUrl: 'https://i.pravatar.cc/40?u=4' },
  { id: '5', name: '<PERSON>', points: 1500, avatarUrl: 'https://i.pravatar.cc/40?u=5' },
];

const currentUserId = '3';

type Payment = {
  id: string;
  amount: number;
  status: 'pending' | 'processing' | 'success' | 'failed';
  email: string;
};

const paymentData: Payment[] = [
    { id: '1', amount: 100, status: 'success', email: '<EMAIL>' },
    { id: '2', amount: 150, status: 'processing', email: '<EMAIL>' },
    { id: '3', amount: 75, status: 'failed', email: '<EMAIL>' },
    { id: '4', amount: 200, status: 'pending', email: '<EMAIL>' },
];

const paymentColumns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'status',
    header: 'Status',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'amount',
    header: 'Amount',
  },
];

const DemoPage = () => {
  const handleUploadComplete = () => {
    alert('Upload complete!');
  };

  const fileUploadData = {
    entidad_relacionada_id: 'd1b2c3a4-e5f6-7890-1234-567890abcdef',
    entidad_relacionada_tipo: 'proceso_cliente',
    usuario_id: currentUserId,
    tipo_grabacion: 'video_pantalla',
  };

  return (
    <div className="container mx-auto p-8 space-y-12 bg-gray-50 min-h-screen">
      <h1 className="text-4xl font-bold text-center text-gray-800 mb-8">Component Demo Page</h1>

      {/* ChallengeCard Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">ChallengeCard</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ChallengeCard
            title="Define un Proceso Clave"
            description="Graba un vídeo explicando cómo se gestionan los pedidos de nuevos clientes."
            points={150}
            status="pending"
            onAction={() => alert('Acción para reto pendiente')}
          />
          <ChallengeCard
            title="Responde a una Pregunta"
            description="¿Cuál es el tiempo medio de resolución de tickets?"
            points={50}
            status="completed"
            onAction={() => {}}
          />
          <ChallengeCard
            title="Analiza un Cuello de Botella"
            points={200}
            status="expired"
            onAction={() => {}}
          />
        </div>
      </section>

      {/* RankingList and FileUpload Section */}
      <section className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        <div>
          <h2 className="text-2xl font-semibold mb-4">RankingList</h2>
          <RankingList users={mockUsers} currentUserId={currentUserId} />
        </div>
        <div>
          <h2 className="text-2xl font-semibold mb-4">FileUpload</h2>
          <FileUpload
            onUploadComplete={handleUploadComplete}
            data={fileUploadData}
          />
        </div>
      </section>

      {/* DataGrid Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">DataGrid</h2>
        <DataGrid columns={paymentColumns} data={paymentData} />
      </section>
    </div>
  );
};

export default DemoPage;