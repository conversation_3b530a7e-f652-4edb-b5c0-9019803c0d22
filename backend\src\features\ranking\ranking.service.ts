import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../common/database.service';

@Injectable()
export class RankingService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findAll() {
    const { data, error } = await this.databaseService
      .getClient()
      .from('usuarios')
      .select('id, nombre, apellidos, puntos, avatar_url')
      .order('puntos', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
}
