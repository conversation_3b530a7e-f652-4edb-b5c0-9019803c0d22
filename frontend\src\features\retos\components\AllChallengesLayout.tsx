import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import apiClient from '@/lib/api';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { RetoCard } from '@/features/dashboard/components/RetoCard';
import { type RetosUsuarios } from '@aceleralia/types';

const getRetos = async (estado: string): Promise<RetosUsuarios[]> => {
  const response = await apiClient.get(`/retos?estado=${estado}`);
  return response.data;
};

export const AllChallengesLayout = () => {
  const [tab, setTab] = useState<'pendientes' | 'completados'>('pendientes');

  const { data: retos, isLoading, isError } = useQuery({
    queryKey: ['retos', tab],
    queryFn: () => getRetos(tab === 'pendientes' ? 'pendiente' : 'completado'),
  });

  return (
    <div className="p-4 md:p-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Todos mis Retos</h1>
        <p className="text-gray-500">
          Aquí puedes ver todos tus retos pendientes y completados.
        </p>
      </header>

      <div className="flex border-b">
        <button
          className={`px-4 py-2 -mb-px font-semibold ${
            tab === 'pendientes'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500'
          }`}
          onClick={() => setTab('pendientes')}
        >
          Pendientes
        </button>
        <button
          className={`px-4 py-2 -mb-px font-semibold ${
            tab === 'completados'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500'
          }`}
          onClick={() => setTab('completados')}
        >
          Completados
        </button>
      </div>

      <div className="mt-8">
        {isLoading && <LoadingSpinner />}
        {isError && <p>Error al cargar los retos.</p>}
        {retos && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {retos.map((reto) => (
              <RetoCard key={reto.id} reto={reto} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};