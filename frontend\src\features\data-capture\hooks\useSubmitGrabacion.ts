import { useMutation, useQueryClient } from '@tanstack/react-query';
import { uploadGrabacion } from '@/lib/api';
import { useToastStore } from '@/store/toast';

export const useSubmitGrabacion = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToastStore();

  return useMutation({
    mutationFn: (data: {
      file: File;
      entidad_relacionada_id: string;
      entidad_relacionada_tipo: string;
      usuario_id: string;
      tipo_grabacion: string;
    }) =>
      uploadGrabacion(data.file, {
        entidad_relacionada_id: data.entidad_relacionada_id,
        entidad_relacionada_tipo: data.entidad_relacionada_tipo,
        usuario_id: data.usuario_id,
        tipo_grabacion: data.tipo_grabacion,
      }),
    onSuccess: () => {
      showToast('Grabación subida correctamente', 'success');
      queryClient.invalidateQueries({ queryKey: ['grabaciones'] });
    },
    onError: (error) => {
      showToast(`Error al subir la grabación: ${error.message}`, 'error');
    },
  });
};