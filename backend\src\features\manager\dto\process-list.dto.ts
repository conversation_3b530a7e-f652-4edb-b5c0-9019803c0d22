import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class ProcessListQueryDto {
  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: 'Items per page', required: false, default: 10 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ description: 'Filter by department', required: false })
  @IsOptional()
  @IsString()
  departamento?: string;

  @ApiProperty({ description: 'Filter by process name', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Sort field',
    required: false,
    default: 'created_at',
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'created_at';

  @ApiProperty({
    description: 'Sort order',
    required: false,
    default: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ProcessListItemDto {
  @ApiProperty({ description: 'Process ID' })
  id: string;

  @ApiProperty({ description: 'Process name' })
  nombre_proceso: string;

  @ApiProperty({ description: 'Process description' })
  descripcion_breve: string;

  @ApiProperty({ description: 'Department name' })
  departamento: string;

  @ApiProperty({ description: 'Responsible person' })
  responsable: string;

  @ApiProperty({ description: 'Process duration in minutes', required: false })
  duracion_minutos?: number;

  @ApiProperty({ description: 'Process frequency' })
  frecuencia: string;

  @ApiProperty({ description: 'Creation date' })
  created_at: string;

  @ApiProperty({ description: 'Number of related tasks' })
  total_tareas: number;

  @ApiProperty({ description: 'Number of related findings' })
  total_hallazgos: number;
}

export class ProcessListResponseDto {
  @ApiProperty({ description: 'List of processes', type: [ProcessListItemDto] })
  data: ProcessListItemDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
