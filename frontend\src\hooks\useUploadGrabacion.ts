import { useMutation } from '@tanstack/react-query';
import { uploadGrabacion } from '../lib/api';

export const useUploadGrabacion = () => {
  return useMutation({
    mutationFn: (variables: {
      file: File;
      body: {
        entidad_relacionada_id: string;
        entidad_relacionada_tipo: string;
        usuario_id: string;
        tipo_grabacion: string;
      };
    }) => uploadGrabacion(variables.file, variables.body),
  });
};