import { registerAs } from '@nestjs/config';

export const N8N_WORKFLOWS = {
  PROCESS_RECORDING: 'processRecording',
  // Add other workflow names here
};

export type N8nWorkflow = (typeof N8N_WORKFLOWS)[keyof typeof N8N_WORKFLOWS];

export default registerAs('n8n', () => ({
  workflows: {
    [N8N_WORKFLOWS.PROCESS_RECORDING]: {
      url: process.env.N8N_PROCESS_RECORDING_WEBHOOK_URL,
    },
    // Add other workflow configurations here
  },
}));
