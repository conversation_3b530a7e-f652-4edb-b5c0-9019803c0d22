import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

export class VincularPersonaDto {
  @ApiProperty({
    description: 'User ID to link to the person',
    example: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
  })
  @IsUUID()
  usuario_id: string;
}

export class VincularPersonaResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Usuario vinculado exitosamente a la persona',
  })
  message: string;

  @ApiProperty({
    description: 'Person ID',
    example: 'c3d4e5f6-a7b8-9012-3456-7890abcdef12',
  })
  persona_id: string;

  @ApiProperty({
    description: 'User ID',
    example: 'b2c3d4e5-f6a7-8901-2345-67890abcdef1',
  })
  usuario_id: string;
}
