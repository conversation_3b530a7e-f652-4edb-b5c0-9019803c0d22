import { ApiProperty } from '@nestjs/swagger';

export class ProcessTaskDto {
  @ApiProperty({ description: 'Task ID' })
  id: string;

  @ApiProperty({ description: 'Task name' })
  nombre_tarea: string;

  @ApiProperty({ description: 'Task description' })
  descripcion: string;

  @ApiProperty({ description: 'Task duration in minutes', required: false })
  duracion_minutos?: number;

  @ApiProperty({ description: 'Task order in process' })
  orden: number;

  @ApiProperty({ description: 'Responsible person', required: false })
  responsable?: string;
}

export class ProcessResponsibleDto {
  @ApiProperty({ description: 'Person ID' })
  id: string;

  @ApiProperty({ description: 'Person name' })
  nombre: string;

  @ApiProperty({ description: 'Person last names' })
  apellidos: string;

  @ApiProperty({ description: 'Person role/position' })
  cargo: string;

  @ApiProperty({ description: 'Department name' })
  departamento: string;

  @ApiProperty({ description: 'Email address' })
  email: string;
}

export class ProcessInformationSourceDto {
  @ApiProperty({ description: 'Information source ID' })
  id: string;

  @ApiProperty({ description: 'Information name' })
  nombre_informacion: string;

  @ApiProperty({ description: 'Information description', required: false })
  descripcion?: string;

  @ApiProperty({ description: 'Information format' })
  formato: string;

  @ApiProperty({ description: 'Person who has the information' })
  persona_responsable: string;

  @ApiProperty({ description: 'Attachment URL', required: false })
  url_adjunto?: string;
}

export class ProcessDetailDto {
  @ApiProperty({ description: 'Process ID' })
  id: string;

  @ApiProperty({ description: 'Process name' })
  nombre_proceso: string;

  @ApiProperty({ description: 'Brief description' })
  descripcion_breve: string;

  @ApiProperty({ description: 'Detailed description' })
  descripcion_detallada: string;

  @ApiProperty({ description: 'Department name' })
  departamento: string;

  @ApiProperty({ description: 'Process duration in minutes', required: false })
  duracion_minutos?: number;

  @ApiProperty({ description: 'Process frequency' })
  frecuencia: string;

  @ApiProperty({ description: 'Process complexity', required: false })
  complejidad?: string;

  @ApiProperty({ description: 'Automation potential', required: false })
  potencial_automatizacion?: string;

  @ApiProperty({ description: 'Creation date' })
  created_at: string;

  @ApiProperty({ description: 'Last update date' })
  updated_at: string;

  @ApiProperty({ description: 'Process tasks', type: [ProcessTaskDto] })
  tareas: ProcessTaskDto[];

  @ApiProperty({
    description: 'Responsible people',
    type: [ProcessResponsibleDto],
  })
  responsables: ProcessResponsibleDto[];

  @ApiProperty({
    description: 'Information sources',
    type: [ProcessInformationSourceDto],
  })
  fuentes_informacion: ProcessInformationSourceDto[];

  @ApiProperty({ description: 'Related findings count' })
  total_hallazgos: number;
}
